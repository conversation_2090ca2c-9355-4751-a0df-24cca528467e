# Stage 1: Base build stage
FROM python:3.12-slim-bookworm AS builder
# Install build dependencies and PDF processing tools
RUN apt-get update && apt-get install -y --no-install-recommends \
build-essential \
gcc \
g++ \
libffi-dev \
tesseract-ocr \
tesseract-ocr-eng \
poppler-utils \
libpoppler-cpp-dev \
&& rm -rf /var/lib/apt/lists/*
# Create the app directory
RUN mkdir /app
# Set the working directory
WORKDIR /app
# Set environment variables to optimize Python
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
# Upgrade pip and install dependencies
RUN pip install --upgrade pip
# Copy the requirements file first (better caching)
COPY requirements.txt /app/
# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Stage 2: Production stage
FROM python:3.12-slim-bookworm
# Install runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
tesseract-ocr \
tesseract-ocr-eng \
poppler-utils \
&& rm -rf /var/lib/apt/lists/*

RUN useradd -m -r appuser && \
mkdir /app && \
mkdir -p /app/static && \
chown -R appuser /app

# Set the working directory
WORKDIR /app

# Copy the Python dependencies from the builder stage
COPY --from=builder /usr/local/lib/python3.12/site-packages/ /usr/local/lib/python3.12/site-packages/
COPY --from=builder /usr/local/bin/ /usr/local/bin/

# Copy application code
COPY --chown=appuser:appuser . /app/

# Set environment variables to optimize Python
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

RUN mkdir -p /app/static && chown -R appuser:appuser /app/static

# Switch to non-root user
USER appuser

# Collect static files
#RUN python manage.py collectstatic --noinput

# Expose the application port
EXPOSE 8000

# This command will be run when the container starts
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "3", "canvider.wsgi:application"]
