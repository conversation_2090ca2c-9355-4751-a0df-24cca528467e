from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_exempt
from .services import analyze_cv, llm_session
from feed.models import Application, Vacancy, ApplicationCvText
from feed.decorators import permission_required
import logging
import json

logger = logging.getLogger(__name__)

@csrf_exempt
@require_POST
@permission_required('manage_candidates')
def analyze_application(request, application_id):
    try:
        print(f"Received request for application ID: {application_id}")
        # Get application and related data - only fetch necessary fields
        application = Application.objects.select_related('vacancy_id') \
            .only('application_id', 'score', 'vacancy_id__vacancy_job_description') \
            .get(pk=application_id)
        vacancy = application.vacancy_id

        # Get the job description
        job_description = vacancy.vacancy_job_description
        if not job_description:
            print(f"Job description not found for vacancy ID: {application_id}")
            return JsonResponse({
                "success": False,
                "message": "Job description not found for this vacancy."
            }, status=404)
        
        # Get the CV text from ApplicationCvText model - only fetch necessary fields
        try:
            cv_text_obj = ApplicationCvText.objects.only(
                'cv_text',
                'is_cv_analyzed',
                'ai_analysis_result'
            ).get(application_id=application)
            cv_text = cv_text_obj.cv_text
            
            if not cv_text or len(cv_text) < 20:
                print(f"CV text is empty or too short for application ID: {application_id}")
                return JsonResponse({
                    "success": False,
                    "message": "CV text is empty or too short for this application."
                }, status=404)
                
        except ApplicationCvText.DoesNotExist:
            print(f"CV text not found for application ID: {application_id}")
            return JsonResponse({
                "success": False,
                "message": "CV text not found for this application."
            }, status=404)
        
        # Check if the CV has already been analyzed
        if cv_text_obj.is_cv_analyzed and cv_text_obj.ai_analysis_result:
            print("CV is already analyzed, using cached result.")
            logger.info(f"Using cached analysis for application {application_id}")
            analysis_result = cv_text_obj.ai_analysis_result
        else:
            print("CV is not analyzed yet, performing analysis.")
            # Call the analyze_cv function to analyze the CV
            analysis_result = analyze_cv(cv_text, job_description)
            
            # Save the analysis result to the CV text object - only update specific fields
            cv_text_obj.ai_analysis_result = analysis_result
            cv_text_obj.is_cv_analyzed = True
            cv_text_obj.save(update_fields=['ai_analysis_result', 'is_cv_analyzed'])

            # Update application score - only update specific field
            Application.objects.filter(pk=application_id) \
                .update(score=analysis_result.get("score", -1))
            
            logger.info(f"Analysis saved for application {application_id}")
            print(f"Analysis result: {analysis_result}")
        print("Response is being sent back to the client.")
        return JsonResponse({
            "success": True,
            "analysis": analysis_result
        })
    except Application.DoesNotExist:
        print(f"Application not found for ID: {application_id}")
        return JsonResponse({
            "success": False,
            "message": "Application not found."
        }, status=404)
    except Exception as e:
        print(f"Error in analyze_application view: {str(e)}")
        logger.error(f"Error in analyze_application view: {str(e)}")
        return JsonResponse({
            "success": False,
            "message": f"An error occurred: {str(e)}"
        }, status=500)

@csrf_exempt
@require_POST
@permission_required('create_jobs')
def generate_job_description(request):
    try:
        job_summary = json.loads(request.body)
        # Start LLM session
        job_description = llm_session(job_summary)
        return JsonResponse({
            "success": True,
            "job_description": job_description['job_description']
        })
    except Exception as e:
        logger.error(f"Error in generate_job_description view: {str(e)}")
        return JsonResponse({
            "success": False,
            "message": f"An error occurred: {str(e)}"
        }, status=500)