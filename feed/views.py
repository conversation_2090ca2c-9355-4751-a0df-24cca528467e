import json
from .forms import InvitationForm
from django.contrib import messages
from django.shortcuts import render, get_object_or_404, redirect, reverse
from django.http import HttpResponse, JsonResponse
from django.utils.dateparse import parse_datetime
from .models import *
import urllib.parse
from django.contrib.auth.models import User
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.db.models import Q, Sum
from django.db import connection
from django.template.defaulttags import register
from django.views.decorators.csrf import csrf_exempt
from datetime import datetime, timedelta
from feed.utils.s3_utils import S3Client
import os
from django.utils import timezone
from django.template.loader import render_to_string
from datetime import datetime
from django.db.models.functions import TruncDay
from dotenv import load_dotenv
from datetime import timedelta
from django.utils import timezone
from django.db.models.functions import <PERSON>run<PERSON><PERSON><PERSON><PERSON>, TruncWeek, TruncDay
from imap_tools import MailBox, AND, OR
import redmail
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.db.models import Count, Max, Min
from django.core.management import call_command
from django.contrib.auth import logout
from .decorators import employer_required, employer_data_access, role_required, permission_required, EmployerQuerySetMixin
from PIL import Image
import base64
from django.utils.translation import gettext_lazy as _, gettext as __
import io
import threading
from django.utils import translation
from django.http import HttpResponseRedirect
from django.conf import settings
from django.contrib import messages
import time
import requests
from werkzeug.utils import secure_filename

s3_client_cv = S3Client("applicants-cv-bucket")
s3_client_photos = S3Client("canvider-public")
load_dotenv()

TOP_APPLICANTS_LIMIT = 5
MAIL_USERNAME = os.environ.get("MAIL_USERNAME")
MAIL_PASSWORD  = os.environ.get("MAIL_PASSWORD")
MAIL_HOST = os.environ.get("IMAP_MAIL_HOST")
SMTP_MAIL_HOST = os.environ.get("SMTP_MAIL_HOST")
SMTP_MAIL_PORT = os.environ.get("SMTP_MAIL_PORT")
GEO_API_KEY = os.environ.get("GEO_API_KEY")
AWS_REGION = os.environ.get("AWS_REGION_NAME")
AWS_SECRET_ACCESS_KEY = os.environ.get("AWS_SECRET_ACCESS_KEY")
AWS_ACCESS_KEY_ID = os.environ.get("AWS_ACCESS_KEY_ID")
AWS_ENDPOINT_URL = os.environ.get("AWS_ENDPOINT_URL")




def ensure_employer_id(request):
    """
    Helper function to ensure request has employer_id set.
    Returns None if successful, or a redirect/JsonResponse if there's an issue.
    """
    if not request.user.is_authenticated:
        return redirect('signin')

    if not hasattr(request, 'employer_id'):
        try:
            employee = request.user.employee
            if employee.status != 'Active':
                return redirect('signin')
            request.employer_id = employee.employer_id.employer_id
            request.employer = employee.employer_id  # Store the Employer object
            request.employee = employee
        except AttributeError:
            return redirect('signin')

    return None  # Success


def ensure_employer_id_json(request):
    """
    Helper function for JSON views to ensure request has employer_id set.
    Returns None if successful, or a JsonResponse if there's an issue.
    """
    if not request.user.is_authenticated:
        return JsonResponse({'success': False, 'message': 'Authentication required'}, status=401)

    if not hasattr(request, 'employer_id'):
        try:
            employee = request.user.employee
            if employee.status != 'Active':
                return JsonResponse({'success': False, 'message': 'Account inactive'}, status=403)
            request.employer_id = employee.employer_id.employer_id
            request.employer = employee.employer_id  # Store the Employer object
            request.employee = employee
        except AttributeError:
            return JsonResponse({'success': False, 'message': 'Account not properly configured'}, status=403)

    return None  # Success


def feed(request):
    # Ensure user has employer_id set
    error_response = ensure_employer_id(request)
    if error_response:
        return error_response

    # Fetch the most recent applicants with their names, positions, and timestamps from last 7 days
    seven_days_ago = timezone.now() - timedelta(days=7)

    # Optimize activity feed query - only fetch necessary fields for template display
    # Filter by employer_id to ensure data isolation
    activity_feed = Application.objects.filter(
        application_date__gte=seven_days_ago,
        vacancy_id__employer_id=request.employer_id  # Add employer filtering
    ).select_related('candidate_id', 'vacancy_id') \
        .only(
            'application_date',
            'candidate_id__candidate_firstname',
            'candidate_id__candidate_lastname',
            'vacancy_id__vacancy_title'
        ) \
        .order_by('-application_date')[:20] \
        .values(
            'candidate_id__candidate_firstname',
            'candidate_id__candidate_lastname',
            'vacancy_id__vacancy_title',
            'application_date'
        )

    # by using the activity_feed, produce the following output for each record: "{candidate first name} {candidate last name} applied for {position} X days ago"
    for activity in activity_feed:
        # Calculate the time difference
        time_difference = timezone.now() - activity['application_date']
        hours_ago = time_difference.total_seconds() // 60

        # Format the output string
        activity['activity_type'] = 'application'
        #activity['formatted_message'] = f"<strong> {activity['candidate_id__candidate_firstname']} {activity['candidate_id__candidate_lastname']} </strong> applied for <strong> {activity['vacancy_id__vacancy_title']} </strong>"
        # Use format string with named placeholders for better translation handling
        message_template = _("<strong>%(name)s</strong> applied for <strong>%(position)s</strong>")
        activity['formatted_message'] = message_template % {
            'name': f"{activity['candidate_id__candidate_firstname']} {activity['candidate_id__candidate_lastname']}",
            'position': activity['vacancy_id__vacancy_title']
        }
        activity['hours_ago'] = hours_ago
        activity['timestamp'] = activity['application_date']

    # Fetch the most recent Application State changes - only necessary fields for template and it shouldn't be new state
    # Filter by employer to ensure data isolation
    application_states = ApplicationState.objects.filter(
        state_started_at__gte=seven_days_ago,
        state_name__in=['Review_1', 'Review_2', 'Review_3', 'Review_4', 'Review_5', 'Ready for Decision', 'Offer Made', 'Candidate Accepted', 'Candidate Rejected', 'Eliminated', 'Hired'],
        application_id__vacancy_id__employer_id=request.employer_id  # Add employer filtering
    ).select_related('application_id__candidate_id', 'application_id__vacancy_id') \
        .only(
            'state_name',
            'state_started_at',
            'application_id__candidate_id__candidate_firstname',
            'application_id__candidate_id__candidate_lastname',
            'application_id__vacancy_id__vacancy_title'
        ) \
        .order_by('-state_started_at') \
        .values(
            'application_id__candidate_id__candidate_firstname',
            'application_id__candidate_id__candidate_lastname',
            'application_id__vacancy_id__vacancy_title',
            'state_name',
            'state_started_at'
        )

    for state in application_states:

        # Calculate the time difference
        time_difference = timezone.now() - state['state_started_at']
        hours_ago = time_difference.total_seconds() // 60

        # Format the output string
        state['activity_type'] = 'state_change'
        #state['formatted_message'] = f"<strong> {state['application_id__candidate_id__candidate_firstname']} {state['application_id__candidate_id__candidate_lastname']} </strong> moved to <strong> {state['state_name']} </strong> for <strong> {state['application_id__vacancy_id__vacancy_title']} </strong>"
        # Use format string with named placeholders for better translation handling
        message_template = _("<strong>%(name)s</strong> moved to <strong>%(state)s</strong> for <strong>%(position)s</strong>")
        state['formatted_message'] = message_template % {
            'name': f"{state['application_id__candidate_id__candidate_firstname']} {state['application_id__candidate_id__candidate_lastname']}",
            'state': _(state['state_name']),
            'position': state['application_id__vacancy_id__vacancy_title']
        }
        state['hours_ago'] = hours_ago
        state['timestamp'] = state['state_started_at']

    # Fetch the most recently published Vacancies - only necessary fields for template
    # Filter by employer to ensure data isolation
    vacancies = Vacancy.objects.filter(
        vacancy_creation_date__gte=seven_days_ago,
        employer_id=request.employer_id  # Add employer filtering
    ).only('vacancy_title', 'vacancy_creation_date') \
        .order_by('-vacancy_creation_date') \
        .values(
            'vacancy_title',
            'vacancy_creation_date'
        )

    for vacancy in vacancies:
        # Calculate the time difference
        time_difference = timezone.now() - vacancy['vacancy_creation_date']
        hours_ago = time_difference.total_seconds() // 60

        # Format the output string
        vacancy['activity_type'] = 'vacancy'
        #vacancy['formatted_message'] = f"A new vacancy <strong> {vacancy['vacancy_title']} </strong> is published"
        #vacancy['formatted_message'] = _("A new vacancy") + " <strong> " + vacancy['vacancy_title'] + " </strong> " + _("is published")
        message_template = _("A new vacancy <strong>%(vacancy_title)s</strong> is published")
        vacancy['formatted_message'] = message_template % {
            'vacancy_title': vacancy['vacancy_title']
        }
        vacancy['hours_ago'] = hours_ago
        vacancy['timestamp'] = vacancy['vacancy_creation_date']



    all_comms = get_all_comments_from_last_days(employer_id=request.employer_id, exclude_user_id=request.user.id)
    print("all_comms", all_comms)
    for comm in all_comms:
        print("comm", comm)
        # Calculate the time difference
        time_difference = timezone.now() - comm['comment_date']
        hours_ago = time_difference.total_seconds() // 60

        # Format the output string
        comm['activity_type'] = 'comment'
        # Fetch the candidate's first name and last name using the application ID
        application = Application.objects.select_related('candidate_id').filter(application_id=comm['application_id']).first()
        if application:
            candidate_firstname = application.candidate_id.candidate_firstname
            candidate_lastname = application.candidate_id.candidate_lastname
            comm['formatted_message'] = _("New comment on application of") + f" <strong> {candidate_firstname} {candidate_lastname} </strong> <a href='/application/{comm['application_id']}'> "+ '<i class="fa-solid fa-link"></i> </a>'
        else:
            comm['formatted_message'] = _("New comment on application ID") + f" <strong> {comm['application_id']} </strong>"
        comm['hours_ago'] = hours_ago
        comm['timestamp'] = comm['comment_date']

    # put only the activity_type, formatted_message, age_msg together
    activity_feed = list(activity_feed) + list(application_states) + list(vacancies) + list(all_comms)

    # Sort the activity feed by hours_ago in descending order
    activity_feed.sort(key=lambda x: x['hours_ago'], reverse=False)

    # Format the time difference into a human-readable format
    for activity in activity_feed:
        minutes_ago = int(activity['hours_ago'])

        if minutes_ago < 1:
            # Just happened
            activity['age_msg'] = _("now")
        elif minutes_ago < 2:
            # 1 minute ago
            activity['age_msg'] = _("1 minute ago")
        elif minutes_ago < 60:
            # 2-59 minutes ago
            activity['age_msg'] = f"{minutes_ago} " + _("minutes ago")
        elif minutes_ago < 120:
            # 1 hour ago (60-119 minutes)
            activity['age_msg'] = _("1 hour ago")
        elif minutes_ago < 1440:
            # 2-23 hours ago
            hours_ago = int(minutes_ago // 60)
            activity['age_msg'] = f"{hours_ago} " + _("hours ago")
        elif minutes_ago < 2880:
            # Yesterday (24-47 hours ago)
            activity['age_msg'] = _("yesterday")
        elif minutes_ago < 10080:
            # 2-6 days ago
            days_ago = int(minutes_ago // 1440)
            activity['age_msg'] = f"{days_ago} " + _("days ago")
        elif minutes_ago < 20160:
            # Last week (7-13 days ago)
            activity['age_msg'] = _("last week")
        elif minutes_ago < 43200:
            # 2-4 weeks ago
            weeks_ago = int(minutes_ago // 10080)
            activity['age_msg'] = f"{weeks_ago} " + _("weeks ago")
        elif minutes_ago < 86400:
            # Last month (30-59 days ago)
            activity['age_msg'] = _("last month")
        elif minutes_ago < 525600:
            # 2-11 months ago
            months_ago = int(minutes_ago // 43200)
            activity['age_msg'] = f"{months_ago} " + _("months ago")
        else:
            # Last year or more
            years_ago = int(minutes_ago // 525600)
            if years_ago == 1:
                activity['age_msg'] = _("last year")
            else:
                activity['age_msg'] = f"{years_ago} " + _("years ago")


    # Check if user has cleared their activity feed
    session_key = f'activity_cleared_{request.employer_id}_{request.user.id}'
    activity_cleared_time = request.session.get(session_key)

    if activity_cleared_time:
        from django.utils.dateparse import parse_datetime
        cleared_timestamp = parse_datetime(activity_cleared_time)
        if cleared_timestamp:
            # Filter out activities that occurred before the clear timestamp
            activity_feed = [
                activity for activity in activity_feed
                if activity.get('timestamp') and activity['timestamp'] > cleared_timestamp
            ]

    # only keep the activity_type, formatted_message, age_msg fields on the activity_feed
    activity_feed = [
        {
            'activity_type': activity['activity_type'],
            'formatted_message': activity['formatted_message'],
            'age_msg': activity['age_msg']
        }
        for activity in activity_feed
    ]

    # get the current date
    now = timezone.now()
    # get the date 7 days ago
    # Get the applicants from the last 7 days, grouped by day - only necessary fields
    # Filter by employer to ensure data isolation
    applicants_last_7_days = (
        Application.objects.filter(
            application_date__gte=seven_days_ago,
            vacancy_id__employer_id=request.employer_id  # Add employer filtering
        )
        .select_related('vacancy_id')
        .only('application_date', 'vacancy_id__vacancy_title', 'vacancy_id__vacancy_city', 'vacancy_id__vacancy_country')
        .annotate(day=TruncDay('application_date'))
        .values('day', 'vacancy_id__vacancy_title', 'vacancy_id__vacancy_city', 'vacancy_id__vacancy_country')
        .annotate(total_applicants=Count('application_id'))
        .order_by('day')
    )

    # Ensure no days are skipped by creating a complete list of days in the last 7 days
    days = [(now - timedelta(days=i)).date() for i in range(7)]
    grouped_applicants = {}

    # Optimize: Only get active vacancies with necessary fields and limit to reduce processing
    # Filter by employer to ensure data isolation
    all_vacancies = Vacancy.objects.filter(
        vacancy_status='Active',
        employer_id=request.employer_id  # Add employer filtering
    ).only('vacancy_title', 'vacancy_city', 'vacancy_country') \
        .values('vacancy_title', 'vacancy_city', 'vacancy_country')[:50]

    for day in days:
        grouped_applicants[day] = [
            {
                'vacancy_id__vacancy_title': vacancy['vacancy_title'],
                'vacancy_id__vacancy_city': vacancy['vacancy_city'],
                'vacancy_id__vacancy_country': vacancy['vacancy_country'],
                'total_applicants': 0
            }
            for vacancy in all_vacancies
        ]

    for applicant in applicants_last_7_days:
        day = applicant['day'].date()
        # Check if the day exists in grouped_applicants before accessing it
        if day in grouped_applicants:
            for vacancy in grouped_applicants[day]:
                if (
                    vacancy['vacancy_id__vacancy_title'] == applicant['vacancy_id__vacancy_title']
                    and vacancy['vacancy_id__vacancy_city'] == applicant['vacancy_id__vacancy_city']
                    and vacancy['vacancy_id__vacancy_country'] == applicant['vacancy_id__vacancy_country']
                ):
                    vacancy['total_applicants'] = applicant['total_applicants']

    # Convert grouped data into the desired format
    applicants_last_7_days = [
        {
            "date": day.strftime("%d.%m.%Y"),
            "vacancies": grouped_applicants[day],
        }
        for day in days
    ]


    # Create hot jobs data by using applicants_last_7_days
    hot_jobs = []
    for day_data in applicants_last_7_days:
        for vacancy in day_data['vacancies']:
            # Check if the vacancy already exists in hot_jobs
            existing_job = next((job for job in hot_jobs if job['title'] == vacancy['vacancy_id__vacancy_title']), None)
            if existing_job:
                # Update the existing job's total_applicants
                existing_job['total_applicants'] += vacancy['total_applicants']
                existing_job['dates'].append(day_data['date'])
                existing_job['applicants'].append(vacancy['total_applicants'])
            else:
                # Create a new job entry
                hot_jobs.append({
                    "title": vacancy['vacancy_id__vacancy_title'],
                    "location": f"{vacancy['vacancy_id__vacancy_city']}, {vacancy['vacancy_id__vacancy_country']}",
                    "total_applicants": vacancy['total_applicants'],
                    "dates": [day_data['date']],
                    "applicants": [vacancy['total_applicants']],
                })
    # Sort hot jobs by total_applicants in descending order
    hot_jobs.sort(key=lambda x: x['total_applicants'], reverse=True)
    # eliminate the hotjobs that have 0 applicants
    hot_jobs = [job for job in hot_jobs if job['total_applicants'] > 0]
    # Limit to the top 4 hot jobs
    hot_jobs = hot_jobs[:4]
    # Add the number of applicants to each job
    for job in hot_jobs:
        job['total_applicants'] = sum(job['applicants'])

    unique_days, new_counts, in_rev_counts, fin_stg_counts, reject_counts = applicant_states_over_time(lookback_days=30, employer_id=request.employer_id)
    overview_totals = get_overview_totals(employer_id=request.employer_id)

    print(f"[FEED-DEBUG] overview_totals: {overview_totals}")

    ## data for the appointment form.
    appointment_data = appointment_form_data(request)

    return render(request, "feed.html", {
        "overview_new":new_counts,
        "overview_in_rev":in_rev_counts,
        "overview_fin_stg":fin_stg_counts,
        "overview_reject":reject_counts,
        "overview_dates": unique_days,
        "activity_feed": activity_feed,
        "overview_sums": overview_totals,
        "hot_jobs": hot_jobs,
        "appointment_form_data": appointment_data
        })

def people(request):
    # Ensure user has employer_id set
    error_response = ensure_employer_id(request)
    if error_response:
        return error_response

    # Get filter parameters
    position_filter = request.GET.get('position', '')
    status_filter = request.GET.get('status', '')
    location_filter = request.GET.get('location', '')
    date_filter = request.GET.get('date', '')
    search_query = request.GET.get('search', '')
    sort_column = request.GET.get('sort', '')
    sort_direction = request.GET.get('direction', 'asc')

    # Start with applications filtered by employer - only show candidates who applied to this employer's jobs
    applications_query = Application.objects.select_related('candidate_id', 'vacancy_id') \
        .filter(vacancy_id__employer_id=request.employer_id) \
        .only(
            'application_id',
            'application_date',
            'application_state',
            'total_exp_years',
            'current_employer',
            'candidate_id__candidate_firstname',
            'candidate_id__candidate_lastname',
            'candidate_id__candidate_email',
            'candidate_id__avatar_bg_color',
            'vacancy_id__vacancy_title',
            'vacancy_id__vacancy_city',
            'vacancy_id__vacancy_country'
        )


    # Apply search filter if provided
    if search_query:
        applications_query = applications_query.filter(
            Q(candidate_id__candidate_firstname__icontains=search_query) |
            Q(candidate_id__candidate_lastname__icontains=search_query) |
            Q(candidate_id__candidate_email__icontains=search_query) |
            Q(vacancy_id__vacancy_title__icontains=search_query)
        )

    # Apply position filter if provided
    if position_filter:
        applications_query = applications_query.filter(vacancy_id__vacancy_title=position_filter)

    # Apply status filter if provided
    if status_filter:
        applications_query = applications_query.filter(application_state=status_filter)

    # Apply location filter if provided
    if location_filter:
        # Split location into city and country if needed
        if "," in location_filter:
            city, country = location_filter.split(",", 1)
            applications_query = applications_query.filter(
                vacancy_id__vacancy_city=city.strip(),
                vacancy_id__vacancy_country=country.strip()
            )
        else:
            # Try to match either city or country
            applications_query = applications_query.filter(
                Q(vacancy_id__vacancy_city=location_filter) |
                Q(vacancy_id__vacancy_country=location_filter)
            )

    # Handle date filter
    from django.utils import timezone
    from datetime import datetime, timedelta

    now = timezone.now()

    if date_filter:
        if date_filter == 'today':
            today = now.date()
            applications_query = applications_query.filter(application_date__date=today)
        elif date_filter == 'this-week':
            start_of_week = now - timedelta(days=now.weekday())
            applications_query = applications_query.filter(application_date__gte=start_of_week)
        elif date_filter == 'this-month':
            start_of_month = datetime(now.year, now.month, 1, tzinfo=now.tzinfo)
            applications_query = applications_query.filter(application_date__gte=start_of_month)

    # Apply sorting
    if sort_column:
        # Map front-end column names to model fields
        sort_mapping = {
            'name': 'candidate_id__candidate_firstname',
            'position': 'vacancy_id__vacancy_title',
            'status': 'application_state',
            'location': 'vacancy_id__vacancy_city',
            'experience': 'total_exp_years',
            'current': 'current_employer',
            'applied': 'application_date',
        }

        if sort_column in sort_mapping:
            order_field = sort_mapping[sort_column]
            if sort_direction == 'desc':
                order_field = '-' + order_field
            applications_query = applications_query.order_by(order_field)
    else:
        # Default sorting - most recent applications first
        applications_query = applications_query.order_by('-application_date')

    # Set up pagination
    applicants_per_page = int(request.GET.get('per_page', 5))  # Get from request or default to 5
    paginator = Paginator(applications_query, applicants_per_page)

    page = request.GET.get('page', 1)

    try:
        applicants = paginator.page(page)
    except PageNotAnInteger:
        applicants = paginator.page(1)
        page = 1
    except EmptyPage:
        applicants = paginator.page(paginator.num_pages)
        page = paginator.num_pages

    # Convert page to int for calculations
    page = int(page)

    # Optimize filter dropdown queries - only fetch from this employer's data
    all_positions = Vacancy.objects.filter(employer_id=request.employer_id) \
        .only('vacancy_title') \
        .values_list('vacancy_title', flat=True).distinct()[:50]
    all_statuses = Application.objects.filter(vacancy_id__employer_id=request.employer_id) \
        .only('application_state') \
        .values_list('application_state', flat=True).distinct()[:20]

    # For locations, combine city and country - only from this employer's active jobs
    all_locations = set()
    location_data = Vacancy.objects.filter(
        employer_id=request.employer_id,
        vacancy_status='Active'
    ).only('vacancy_city', 'vacancy_country') \
        .values('vacancy_city', 'vacancy_country').distinct()[:50]
    for location in location_data:
        if location['vacancy_city'] and location['vacancy_country']:
            all_locations.add(f"{location['vacancy_city']}, {location['vacancy_country']}")
        elif location['vacancy_city']:
            all_locations.add(location['vacancy_city'])
        elif location['vacancy_country']:
            all_locations.add(location['vacancy_country'])

    # Convert to sorted list for template
    all_locations = sorted(list(all_locations))

    # Create active filter tags for display
    active_filters = []
    if search_query:
        active_filters.append(('search', search_query))
    if position_filter:
        active_filters.append(('position', position_filter))
    if status_filter:
        active_filters.append(('status', status_filter))
    if location_filter:
        active_filters.append(('location', location_filter))
    if date_filter:
        date_filter_display = {
            'today': 'Today',
            'this-week': 'This Week',
            'this-month': 'This Month'
        }.get(date_filter, date_filter)
        active_filters.append(('date', date_filter_display))

    # Calculate pagination info for display
    total_applicants = paginator.count
    start_index = (page - 1) * applicants_per_page + 1 if total_applicants > 0 else 0
    end_index = min(start_index + applicants_per_page - 1, total_applicants)

    # Optimize: Prefetch ApplicationCvText for all applicants in one query instead of N+1 queries
    application_ids = [app.application_id for app in applicants]
    cv_text_objects = ApplicationCvText.objects.filter(application_id__in=application_ids) \
        .select_related('application_id') \
        .only('application_id', 'cv_text', 'is_cv_analyzed', 'ai_analysis_result')
    cv_text_dict = {cv.application_id.application_id: cv for cv in cv_text_objects}

    # Assign cv_text_obj to each applicant
    for applicant in applicants:
        applicant.cv_text_obj = cv_text_dict.get(applicant.application_id, None)

    context = {
        "applicants": applicants,
        "all_positions": all_positions,
        "all_statuses": all_statuses,
        "all_locations": all_locations,
        "active_filters": active_filters,
        # Pass current filter values for form state persistence
        "position_filter": position_filter,
        "status_filter": status_filter,
        "location_filter": location_filter,
        "date_filter": date_filter,
        "search_query": search_query,
        # Pagination context
        "page_number": page,
        "total_pages": paginator.num_pages,
        "applicants_per_page": applicants_per_page,
        "total_applicants": total_applicants,
        "start_index": start_index,
        "end_index": end_index,
    }

    return render(request, "people.html", context)

def jobs(request):
    # Ensure user has employer_id set
    error_response = ensure_employer_id(request)
    if error_response:
        return error_response

    # Get filter parameters
    department_filter = request.GET.get('department', '')
    status_filter = request.GET.get('status', '')
    location_filter = request.GET.get('location', '')
    date_filter = request.GET.get('date', '')

    # Start with vacancies filtered by employer - only fetch fields needed for jobs.html template
    vacancies_query = Vacancy.objects.exclude(vacancy_status="Deleted") \
        .filter(employer_id=request.employer_id) \
        .only(
            'vacancy_id',
            'vacancy_title',
            'vacancy_city',
            'vacancy_country',
            'vacancy_creation_date',
            'vacancy_status',
            'vacancy_bus_unit',
            'number_of_applicants_temp'
        )

    # Add this within the jobs view, at the beginning of the filtering logic
    search_query = request.GET.get('search', '')
    if search_query:
        vacancies_query = vacancies_query.filter(
            Q(vacancy_title__icontains=search_query) |
            Q(vacancy_bus_unit__icontains=search_query) |
            Q(vacancy_city__icontains=search_query) |
            Q(vacancy_country__icontains=search_query)
        )

    # Apply filters if provided
    if department_filter:
        vacancies_query = vacancies_query.filter(vacancy_bus_unit=department_filter)

    if status_filter:
        vacancies_query = vacancies_query.filter(vacancy_status=status_filter)

    if location_filter:
        # Split location into city and country if needed
        if "," in location_filter:
            city, country = location_filter.split(",", 1)
            vacancies_query = vacancies_query.filter(vacancy_city=city.strip(), vacancy_country=country.strip())
        else:
            # Try to match either city or country
            vacancies_query = vacancies_query.filter(
                Q(vacancy_city=location_filter) | Q(vacancy_country=location_filter)
            )

    # Handle date filter
    from django.utils import timezone
    from datetime import datetime, timedelta

    now = timezone.now()

    if date_filter == 'today':
        today = now.date()
        vacancies_query = vacancies_query.filter(vacancy_creation_date__date=today)
    elif date_filter == 'this-week':
        start_of_week = now - timedelta(days=now.weekday())
        vacancies_query = vacancies_query.filter(vacancy_creation_date__gte=start_of_week)
    elif date_filter == 'this-month':
        start_of_month = datetime(now.year, now.month, 1, tzinfo=now.tzinfo)
        vacancies_query = vacancies_query.filter(vacancy_creation_date__gte=start_of_month)
    elif date_filter == 'last-month':
        last_month = now.month - 1
        last_month_year = now.year
        if last_month == 0:
            last_month = 12
            last_month_year -= 1
        start_of_last_month = datetime(last_month_year, last_month, 1, tzinfo=now.tzinfo)
        end_of_last_month = datetime(now.year, now.month, 1, tzinfo=now.tzinfo)
        vacancies_query = vacancies_query.filter(
            vacancy_creation_date__gte=start_of_last_month,
            vacancy_creation_date__lt=end_of_last_month
        )


    # Number of jobs to display per page
    jobs_per_page = 10 # divs are split either 3 or 4 or 5.
    paginator = Paginator(vacancies_query, jobs_per_page)

    # Get the requested page number from the URL
    page = request.GET.get('page', 1)

    try:
        # Get the specified page of vacancies
        vacancies = paginator.page(page)
    except PageNotAnInteger:
        # If page is not an integer, deliver first page
        vacancies = paginator.page(1)
        page = 1
    except EmptyPage:
        # If page is out of range, deliver last page
        vacancies = paginator.page(paginator.num_pages)
        page = paginator.num_pages

    # Convert page to int for calculations
    page = int(page)

    # Get unique values for filter options - only fetch necessary fields from employer's vacancies
    all_departments = Vacancy.objects.filter(employer_id=request.employer_id) \
        .only('vacancy_bus_unit') \
        .values_list('vacancy_bus_unit', flat=True).distinct()
    all_statuses = Vacancy.objects.filter(employer_id=request.employer_id) \
        .only('vacancy_status') \
        .values_list('vacancy_status', flat=True).distinct()

    # For locations, combine city and country - only fetch necessary fields from employer's vacancies
    all_locations = set()
    location_data = Vacancy.objects.filter(employer_id=request.employer_id) \
        .only('vacancy_city', 'vacancy_country') \
        .values('vacancy_city', 'vacancy_country').distinct()
    for location in location_data:
        if location['vacancy_city'] and location['vacancy_country']:
            all_locations.add(f"{location['vacancy_city']}, {location['vacancy_country']}")
        elif location['vacancy_city']:
            all_locations.add(location['vacancy_city'])
        elif location['vacancy_country']:
            all_locations.add(location['vacancy_country'])

    # Convert to sorted list for template
    all_locations = sorted(list(all_locations))

    # Calculate additional data for each vacancy
    for vacancy in vacancies:
        # Calculate days open
        days_open = (now - vacancy.vacancy_creation_date).days
        vacancy.days_open = days_open + 1

        # Calculate interview count
        vacancy.interview_count = 0

        # Check if there are applications for this vacancy and count them - optimized query
        application_count = Application.objects.filter(vacancy_id=vacancy.vacancy_id) \
            .only('application_id').count()
        if application_count > 0:
            vacancy.number_of_applicants_temp = application_count

    # Calculate stats for this employer - optimized queries with only necessary fields
    active_jobs_count = Vacancy.objects.filter(
        vacancy_status="Active",
        employer_id=request.employer_id
    ).only('vacancy_id').count()

    # Count applications for this employer's vacancies
    total_applicants_count = Application.objects.filter(
        vacancy_id__employer_id=request.employer_id
    ).only('application_id').count()

    archived_jobs = Vacancy.objects.filter(
        vacancy_status="Archived",
        employer_id=request.employer_id
    ).only('vacancy_id').count()

    on_hold_jobs = Vacancy.objects.filter(
        vacancy_status="On-Hold",
        employer_id=request.employer_id
    ).only('vacancy_id').count()

    # Create active filter tags for display
    active_filters = []

    if search_query:
        active_filters.append(('search', search_query))
    if department_filter:
        active_filters.append(('department', department_filter))
    if status_filter:
        active_filters.append(('status', status_filter))
    if location_filter:
        active_filters.append(('location', location_filter))
    if date_filter:
        date_filter_display = {
            'today': 'Today',
            'this-week': 'This Week',
            'this-month': 'This Month',
            'last-month': 'Last Month'
        }.get(date_filter, date_filter)
        active_filters.append(('date', date_filter_display))

    # Calculate pagination info for the display
    total_jobs = paginator.count
    start_index = (page - 1) * jobs_per_page + 1
    end_index = min(start_index + jobs_per_page - 1, total_jobs)

    context = {
        "vacancies": vacancies,
        "active_jobs_count": active_jobs_count,
        "total_applicants_count": total_applicants_count,
        "archived_jobs": archived_jobs,
        "on_hold_jobs": on_hold_jobs,
        "all_departments": all_departments,
        "all_statuses": all_statuses,
        "all_locations": all_locations,
        "active_filters": active_filters,
        # Pass current filter values for form state persistence
        "department_filter": department_filter,
        "status_filter": status_filter,
        "location_filter": location_filter,
        "date_filter": date_filter,
        "search_query": search_query,
        # Pagination context
        "page_number": page,
        "total_pages": paginator.num_pages,
        "jobs_per_page": jobs_per_page,
        "total_jobs": total_jobs,
        "start_index": start_index,
        "end_index": end_index,
    }

    return render(request, "jobs.html", context)

@permission_required('create_jobs')
def create_job(request):
    # Ensure user has employer_id set
    error_response = ensure_employer_id(request)
    if error_response:
        return error_response

    # Get the employer object for filtering related data
    employer_id = request.employer

    # Get office locations for this employer - only necessary fields for template
    office_locations = OfficeLocation.objects.filter(employer_id=employer_id) \
        .only('id', 'city', 'country') \
        .order_by('city')

    # Get work schedules for this employer - only necessary fields for template
    work_schedules = WorkSchedule.objects.filter(employer_id=employer_id) \
        .only('id', 'name') \
        .order_by('name')

    # Get office schedules for this employer - only necessary fields for template
    office_schedules = OfficeSchedule.objects.filter(employer_id=employer_id) \
        .only('id', 'name') \
        .order_by('name')

    return render(request, "create_job.html", {
        "office_locations": office_locations,
        "work_schedules": work_schedules,
        "office_schedules": office_schedules,
        "has_locations": office_locations.exists(),
        "has_work_schedules": work_schedules.exists(),
        "has_office_schedules": office_schedules.exists(),
    })

def job_description(request):
    return render(request, "job_details.html")


def appointment_form_data(request):
    # Ensure user has employer_id set
    error_response = ensure_employer_id(request)
    if error_response:
        return {}  # Return empty dict for form data if there's an error

    all_appointment_data = {}
    all_appointment_data['appointment_kinds'] = [kind[0] for kind in Appointment.APPOINTMENT_KINDS]
    all_appointment_data['possible_interviewers'] = Employee.objects.filter(employer_id=request.employer) \
        .select_related('user') \
        .only('user__email') \
        .values_list('user__email', flat=True)
    # Update this line to include both id and title - only necessary fields
    all_appointment_data['possible_vacancies'] = Vacancy.objects.filter(employer_id=request.employer_id) \
        .only('vacancy_id', 'vacancy_title') \
        .values('vacancy_id', 'vacancy_title')

    return all_appointment_data


def job_preview_publish(request):
    # Ensure user has employer_id set
    error_response = ensure_employer_id(request)
    if error_response:
        return error_response

    # get the Portal Configurations with Active status for the current user's employer
    active_portal_names = []
    try:
        portal_configurations = PortalConfigurations.objects.filter(
            employer_id=request.employer_id,
            portal_status="Active"
        )
        for portal in portal_configurations:
            if portal.portal_status == "Active":
                active_portal_names.append(portal.portal_name)
            else:
                active_portal_names = []
    except:
        active_portal_names = []

    active_portal_dict = {portal_name: True for portal_name in active_portal_names}
    print("Active portal names:", active_portal_dict)

    return render(request, "job_preview_publish.html", {"active_portals": active_portal_dict})


def profile(request):
    try:
        # Optimize employee query - only fetch necessary fields for profile template
        employee = Employee.objects.select_related('user', 'employer_id') \
            .only(
                'role',
                'status',
                'created_at',
                'profile_photo',
                'user__first_name',
                'user__last_name',
                'user__email',
                'user__username',
                'employer_id__employer_name'
            ).get(user=request.user)
        user = employee.user
        print("Employee found:", employee)
        print("User found:", user)
        return render(request, "profile.html", {"recruiter": employee, "user": user})
    except Employee.DoesNotExist:
        return render(request, "profile.html", {"recruiter": None, "user": None})

def change_password_user(request):
    if request.method == 'POST':
        current_password = request.POST.get('current_password')
        new_password = request.POST.get('new_password')
        confirm_password = request.POST.get('confirm_password')

        if new_password != confirm_password:
            messages.error(request, "Passwords do not match.")
            return redirect('profile')

        if not request.user.check_password(current_password):
            messages.error(request, "Current password is incorrect.")
            return redirect('profile')

        request.user.set_password(new_password)
        request.user.save()
        logout(request)
        messages.success(request, "Password changed successfully! Please login with your new password.")
        return redirect('signin')


def change_employee_photo(request):
    if request.method == 'POST':
        photo = request.FILES.get('photo')
        if photo:
            try:
                # Open the image and resize it to a smaller size
                img = Image.open(photo)
                # Create a copy for the navbar (smaller size)
                img_small = img.copy()
                img_small.thumbnail((60, 60))

                # Convert images to base64 data URLs
                # Save the small image in memory
                buffer = io.BytesIO()
                img_small.save(buffer, format="PNG")
                img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
                img_data_url = f"data:image/png;base64,{img_base64}"

                # Save to the employee model - only update profile_photo field
                Employee.objects.filter(user=request.user) \
                    .update(profile_photo=img_data_url)

                messages.success(request, _("Profile photo changed successfully!"))
            except Exception as e:
                print(f"Error processing image: {e}")
                messages.error(request, f"Error processing image: {e}")
        else:
            messages.error(request, _("Please select a photo."))
        return redirect('profile')


@role_required(['Administrator', 'Recruiter'])
def settings(request):
    # DEBUG: Print request attributes
    print(f"[SETTINGS-DEBUG] User: {request.user}")
    print(f"[SETTINGS-DEBUG] User authenticated: {request.user.is_authenticated}")
    print(f"[SETTINGS-DEBUG] Has employer_id: {hasattr(request, 'employer_id')}")
    if hasattr(request, 'employer_id'):
        print(f"[SETTINGS-DEBUG] Employer ID: {request.employer_id}")
    if hasattr(request, 'employee'):
        print(f"[SETTINGS-DEBUG] Employee: {request.employee}")
        print(f"[SETTINGS-DEBUG] Employee role: {request.employee.role}")

    delete_old_cv_files()

    # Trigger data cleanup for deleted vacancies (runs irregularly)
    try:
        cleanup_stats = cleanup_deleted_vacancy_data()
        if cleanup_stats['vacancies_processed'] > 0:
            print(f"[SETTINGS-DEBUG] Data cleanup completed: {cleanup_stats}")
    except Exception as e:
        # Don't show error to user, just log it
        print(f"[SETTINGS-DEBUG] Error during data cleanup: {str(e)}")

    return render(request, "settings.html")


def published_job_details_with_id(request, vacancy_id):
    # Ensure user has employer_id set
    error_response = ensure_employer_id(request)
    if error_response:
        return error_response

    # Get vacancy filtered by employer to ensure data isolation
    vacancy = get_object_or_404(Vacancy.objects.filter(
        employer_id=request.employer_id
    ).only(
        'vacancy_id', 'vacancy_title', 'vacancy_status', 'vacancy_creation_date', 'vacancy_job_description', 'skills'
    ), vacancy_id=vacancy_id)

    if vacancy.vacancy_status == "Deleted":
        messages.info(request, "This vacancy no longer exists.")
        return redirect('jobs')
    
    # convert vacancy skills to an easier list to work with.
    vacancy.skills = json.loads(vacancy.skills)

    

    # Optimize top applicants query - only fetch necessary fields for template
    top_applicants = Application.objects.filter(vacancy_id=vacancy_id) \
        .select_related('candidate_id') \
        .only(
            'application_id',
            'score',
            'candidate_id__candidate_firstname',
            'candidate_id__candidate_lastname'
        ) \
        .order_by("-score")[:TOP_APPLICANTS_LIMIT]

    print("Top applicants for vacancy:", vacancy_id)

    # Optimize applicants over time query - only necessary fields
    applicants_over_time = (
        Application.objects.filter(vacancy_id=vacancy_id)
        .only('application_date', 'application_id')
        .annotate(week=TruncDay('application_date'))
        .values('week')
        .annotate(count=Count('application_id'))
        .order_by('week')
    )

    # Optimize application count query
    application_count = Application.objects.filter(vacancy_id=vacancy.vacancy_id) \
        .only('application_id').count()

    # Optimize applications by source query
    applications_by_source = (
        Application.objects.filter(vacancy_id=vacancy_id)
        .only('application_source', 'application_id')
        .values('application_source')
        .annotate(count=Count('application_id'))
        .order_by('application_source')
    )

    days_open = (timezone.now() - vacancy.vacancy_creation_date).days
    vacancy.days_open = days_open + 1

    # Optimize applications by state query
    applications_by_state = (
        Application.objects.filter(vacancy_id=vacancy_id)
        .only('application_state', 'application_id')
        .values('application_state')
        .annotate(count=Count('application_id'))
        .order_by('application_state')
    )

    # Convert the vacancy title to a URL-friendly format

    all_applicants_url = urllib.parse.quote(vacancy.vacancy_title, safe='')


    return render(request,
                   "published_job_details.html",
                     {
                         "vacancy": vacancy,
                         "application_count": application_count,
                         "top_applicants":top_applicants,
                         "applicants_over_time": applicants_over_time,
                         "applications_by_state":applications_by_state,
                         "all_applicants_url": all_applicants_url,
                         "applications_by_source": applications_by_source,})

def application(request, application_id):
    # Ensure user has employer_id set and validate access to this application
    error_response = ensure_employer_id(request)
    if error_response:
        return error_response

    # Verify that the application belongs to the user's employer
    try:
        # First check if the application exists and belongs to user's employer
        test_application = Application.objects.select_related('vacancy_id').get(
            application_id=application_id,
            vacancy_id__employer_id=request.employer_id
        )
    except Application.DoesNotExist:
        messages.error(request, "You don't have permission to access this application.")
        return redirect('people')

    # Optimize application query - only fetch necessary fields for template
    # Filter by employer to ensure data isolation
    application = get_object_or_404(
        Application.objects.select_related('candidate_id', 'vacancy_id')
        .filter(vacancy_id__employer_id=request.employer_id)  # Add employer filtering
        .only(
            'application_id',
            'application_state',
            'cv_location',
            'candidate_id__candidate_id',
            'candidate_id__candidate_firstname',
            'candidate_id__candidate_lastname',
            'candidate_id__candidate_email',
            'candidate_id__candidate_phone',
            'candidate_id__candidate_address',
            'candidate_id__candidate_date_of_birth',
            'candidate_id__avatar_bg_color',
            'vacancy_id__vacancy_id',
            'vacancy_id__vacancy_title',
            'vacancy_id__employer_id'
        ),
        application_id=application_id
    )

    vacancy_id = application.vacancy_id.vacancy_id
    # Optimize job description query - only fetch necessary field
    job_description = Vacancy.objects.filter(vacancy_id=vacancy_id) \
        .only('vacancy_job_description') \
        .values_list('vacancy_job_description', flat=True).first()

    if job_description is None:
        job_description = "No job description available."

    candidate = application.candidate_id

    # Optimize comments query - only fetch necessary fields for template
    comments_of_application = ApplicationComment.objects.filter(
        application_id=application_id
    ).select_related('commented_by') \
    .only(
        'comment_id',
        'comment_body',
        'comment_date',
        'commented_by__first_name',
        'commented_by__last_name'
    ).order_by('-comment_date')

    # Optimize state query - only fetch necessary fields for template
    state_of_applicant = ApplicationState.objects.filter(
        application_id=application_id
    ).select_related('committed_by') \
    .only(
        'state_name',
        'state_notes',
        'state_started_at',
        'committed_by__first_name',
        'committed_by__last_name'
    )

    try:
        cv_text_obj = ApplicationCvText.objects.get(application_id=application)
    except ApplicationCvText.DoesNotExist:
        # If it doesn't exist, create a new one with default values
        cv_text_obj = ApplicationCvText(
            application_id=application,
            is_cv_analyzed=False,
            ai_analysis_result=None
        )
        cv_text_obj.save()

    # Use ai_analysis_result from cv_text_obj if available, otherwise use default
    if cv_text_obj.is_cv_analyzed and cv_text_obj.ai_analysis_result:
        ai_data = cv_text_obj.ai_analysis_result
    else:
        # Fallback default AI data (this will be displayed if no analysis has been run)
        ai_data = {
            "score": 0,
            "summary": "No AI analysis has been performed yet.",
            "highlights": [],
            "drawbacks": []
        }

    # Collect the CV from the S3 by using the cv_location
    cv_location = application.cv_location
    if cv_location:
        try:
            print("trying to download CV from S3...")
            # Download or replace the file from S3
            cv_file = s3_client_cv.download_file(cv_location)
            local_cv_address = f"cv/{application_id}.pdf"
            with open(f"static/{local_cv_address}", 'wb') as f:
                f.write(cv_file)

            application.cv_location = local_cv_address  # Update the location for use in the template
        except Exception as e:
            print(f"Error downloading CV from S3: {e}")
            application.cv_location = "cv/no_cv.pdf"  # Fallback to placeholder for no CV
    else:
        application.cv_location = "cv/no_cv.pdf"  # Placeholder for no CV


    # Collect emails
    email_subject_key = f'#V{application.vacancy_id.vacancy_id}E{Vacancy.objects.get(vacancy_id=application.vacancy_id.vacancy_id).employer_id}A{application_id}'
    emails = get_mails_with_key(email_subject_key=email_subject_key)

    # Birhan buraya bak
    # lang_info = translation.get_language_info(translation.get_language())["name"]
    # print(f"[LANGUAGE-DEBUG] Current language: {lang_info}")

    # Latest email date
    latest_email_date = None
    if emails:
        latest_email_date = emails[0].date.astimezone()

    return render(
        request,
        "applicant_dev.html",
        {
            "application": application,
            "candidate_info": candidate,
            "comments_data": comments_of_application,
            "stage_data": state_of_applicant,
            "ai_data": ai_data,
            "emails_data": emails,
            "email_subject_key": email_subject_key,
            "cv_text_obj": cv_text_obj,
            "job_description": job_description,
            "appointment_form_data": appointment_form_data(request),
            "latest_email_date": latest_email_date
        },
    )


# a cron job to delete cv files that are older than 10 hours except the no_cv.pdf
def delete_old_cv_files():
    # Get the current time
    now = datetime.now()

    # Define the directory where CV files are stored
    cv_directory = 'static/cv/'

    # Loop through all files in the directory
    for filename in os.listdir(cv_directory):
        if filename != 'no_cv.pdf':
            file_path = os.path.join(cv_directory, filename)
            # Get the file's last modified time
            file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
            # Check if the file is older than 10 hours
            if now - file_mtime > timedelta(hours=10):
                print("old CV deleted...")
                os.remove(file_path)  # Delete the file


def cleanup_deleted_vacancy_data():
    """
    Clean up candidate and application data for vacancies that have been deleted
    for more than 30 days. This protects candidate privacy by removing their data
    after a reasonable period.
    """
    from django.utils import timezone
    from django.db import transaction
    import logging

    logger = logging.getLogger(__name__)

    try:
        # Calculate the cutoff date (30 days ago)
        cutoff_date = timezone.now() - timedelta(days=30)

        # Find vacancies that were deleted more than 30 days ago
        deleted_vacancies = Vacancy.objects.filter(
            vacancy_status="Deleted",
            vacancy_creation_date__lt=cutoff_date
        )

        cleanup_stats = {
            'vacancies_processed': 0,
            'applications_deleted': 0,
            'candidates_deleted': 0,
            'comments_deleted': 0,
            'appointments_deleted': 0
        }

        with transaction.atomic():
            for vacancy in deleted_vacancies:
                vacancy_id = vacancy.vacancy_id
                logger.info(f"Processing cleanup for deleted vacancy: {vacancy_id}")

                # Get all applications for this vacancy
                applications = Application.objects.filter(vacancy_id=vacancy_id)
                application_count = applications.count()

                # Get all candidate IDs from these applications
                candidate_ids = applications.values_list('candidate_id', flat=True).distinct()

                # Delete application comments
                comments_deleted = ApplicationComment.objects.filter(
                    application_id__in=applications.values_list('application_id', flat=True)
                ).delete()[0]

                # Delete appointments related to this vacancy
                appointments_deleted = Appointment.objects.filter(
                    vacancy_id=vacancy_id
                ).delete()[0]

                # Delete applications
                applications.delete()

                # Delete candidates that were only associated with this vacancy
                # (candidates who haven't applied to other active vacancies)
                candidates_to_delete = []
                for candidate_id in candidate_ids:
                    # Check if this candidate has applications to other non-deleted vacancies
                    other_applications = Application.objects.filter(
                        candidate_id=candidate_id
                    ).exclude(vacancy_id=vacancy_id).exists()

                    if not other_applications:
                        candidates_to_delete.append(candidate_id)

                candidates_deleted = Candidate.objects.filter(
                    candidate_id__in=candidates_to_delete
                ).delete()[0]

                # Update stats
                cleanup_stats['vacancies_processed'] += 1
                cleanup_stats['applications_deleted'] += application_count
                cleanup_stats['candidates_deleted'] += candidates_deleted
                cleanup_stats['comments_deleted'] += comments_deleted
                cleanup_stats['appointments_deleted'] += appointments_deleted

                logger.info(f"Cleaned up vacancy {vacancy_id}: {application_count} applications, "
                           f"{candidates_deleted} candidates, {comments_deleted} comments, "
                           f"{appointments_deleted} appointments")

        logger.info(f"Data cleanup completed: {cleanup_stats}")
        return cleanup_stats

    except Exception as e:
        logger.error(f"Error during data cleanup: {str(e)}")
        raise e


def generate_captcha():
    """Generate a simple math captcha"""
    import random
    num1 = random.randint(1, 10)
    num2 = random.randint(1, 10)
    operation = random.choice(['+', '-', '*'])

    if operation == '+':
        answer = num1 + num2
        question = f"{num1} + {num2}"
    elif operation == '-':
        # Ensure positive result
        if num1 < num2:
            num1, num2 = num2, num1
        answer = num1 - num2
        question = f"{num1} - {num2}"
    else:  # multiplication
        answer = num1 * num2
        question = f"{num1} × {num2}"

    return question, answer


def signin(request):
    from django.contrib.auth import authenticate, login

    # If user is already authenticated, redirect to feed
    if request.user.is_authenticated:
        try:
            # Verify user has valid employee record
            employee = request.user.employee
            if employee.status == 'Active':
                return redirect('feed')
            else:
                # If employee is inactive, logout and show error
                logout(request)
                messages.error(request, "Your account is inactive. Please contact support.")
        except AttributeError:
            # If no employee record, logout and show error
            logout(request)
            messages.error(request, "Your account is not properly configured. Please contact support.")

    # Generate captcha for GET requests or if captcha is missing from session
    if request.method == "GET" or 'captcha_answer' not in request.session:
        captcha_question, captcha_answer = generate_captcha()
        request.session['captcha_question'] = captcha_question
        request.session['captcha_answer'] = captcha_answer

    if request.method == 'POST':
        email = request.POST.get('email')
        password = request.POST.get('password')
        captcha_input = request.POST.get('captcha')
        remember_me = request.POST.get('remember_me')

        # Validate captcha first
        if not captcha_input:
            messages.error(request, "Please solve the math problem.")
            # Generate new captcha
            captcha_question, captcha_answer = generate_captcha()
            request.session['captcha_question'] = captcha_question
            request.session['captcha_answer'] = captcha_answer
            return render(request, "signin.html", {
                'captcha_question': captcha_question
            })

        try:
            captcha_answer_input = int(captcha_input)
            expected_answer = request.session.get('captcha_answer')

            if captcha_answer_input != expected_answer:
                messages.error(request, "Incorrect answer to the math problem. Please try again.")
                # Generate new captcha
                captcha_question, captcha_answer = generate_captcha()
                request.session['captcha_question'] = captcha_question
                request.session['captcha_answer'] = captcha_answer
                return render(request, "signin.html", {
                    'captcha_question': captcha_question
                })
        except (ValueError, TypeError):
            messages.error(request, "Please enter a valid number for the math problem.")
            # Generate new captcha
            captcha_question, captcha_answer = generate_captcha()
            request.session['captcha_question'] = captcha_question
            request.session['captcha_answer'] = captcha_answer
            return render(request, "signin.html", {
                 'captcha_question': captcha_question
             })

        if email and password:
            # Authenticate user (custom backend handles email or username)
            user = authenticate(request, username=email, password=password)

            if user is not None:
                # Check if user has an associated employee record
                try:
                    employee = user.employee
                    if employee.status != 'Active':
                        messages.error(request, "Your account is inactive. Please contact support.")
                        # Generate new captcha for next attempt
                        captcha_question, captcha_answer = generate_captcha()
                        request.session['captcha_question'] = captcha_question
                        request.session['captcha_answer'] = captcha_answer
                        return render(request, "signin.html", {
                            'captcha_question': captcha_question
                        })

                    # Login the user
                    login(request, user)

                    # Clear captcha from session after successful login
                    request.session.pop('captcha_question', None)
                    request.session.pop('captcha_answer', None)

                    # Set session expiry based on remember_me
                    if not remember_me:
                        request.session.set_expiry(0)  # Session expires when browser closes
                    else:
                        request.session.set_expiry(1209600)  # 2 weeks

                    # Get the next URL if provided, otherwise go to feed
                    next_url = request.GET.get('next', 'feed')
                    return redirect(next_url)

                except AttributeError:
                    messages.error(request, "Your account is not properly configured. Please contact support.")
                    # Generate new captcha
                    captcha_question, captcha_answer = generate_captcha()
                    request.session['captcha_question'] = captcha_question
                    request.session['captcha_answer'] = captcha_answer
                    return render(request, "signin.html", {
                        'captcha_question': captcha_question
                    })
            else:
                messages.error(request, "Invalid email or password.")
                # Generate new captcha
                captcha_question, captcha_answer = generate_captcha()
                request.session['captcha_question'] = captcha_question
                request.session['captcha_answer'] = captcha_answer
                return render(request, "signin.html", {
                    'captcha_question': captcha_question
                })
        else:
            messages.error(request, "Please provide both email and password.")
            # Generate new captcha
            captcha_question, captcha_answer = generate_captcha()
            request.session['captcha_question'] = captcha_question
            request.session['captcha_answer'] = captcha_answer
            return render(request, "signin.html", {
                'captcha_question': captcha_question
            })

    return render(request, "signin.html", {
        'captcha_question': request.session.get('captcha_question', '')
    })


def signout(request):
    """
    Logout view that clears session and redirects to signin page.
    """
    logout(request)
    messages.success(request, "You have been successfully logged out.")
    return redirect('signin')


def test_auth(request):
    """
    Test view to check authentication status and employer isolation.
    """
    from django.http import JsonResponse
    from django.contrib.auth.models import User
    from .models import Employee, Employer

    # Get some debug info
    total_users = User.objects.count()
    total_employees = Employee.objects.count()
    total_employers = Employer.objects.count()

    if request.user.is_authenticated:
        try:
            employee = request.user.employee
            return JsonResponse({
                'authenticated': True,
                'user': request.user.username,
                'user_id': request.user.id,
                'employee_id': employee.user.id,
                'employer_id': employee.employer_id.employer_id,
                'employer_name': employee.employer_id.employer_name,
                'role': employee.role,
                'status': employee.status,
                'middleware_employer_id': getattr(request, 'employer_id', 'Not set'),
                'debug_info': {
                    'total_users': total_users,
                    'total_employees': total_employees,
                    'total_employers': total_employers,
                }
            })
        except AttributeError as e:
            return JsonResponse({
                'authenticated': True,
                'user': request.user.username,
                'user_id': request.user.id,
                'error': f'No employee record found: {str(e)}',
                'debug_info': {
                    'total_users': total_users,
                    'total_employees': total_employees,
                    'total_employers': total_employers,
                }
            })
    else:
        return JsonResponse({
            'authenticated': False,
            'message': 'User not authenticated',
            'debug_info': {
                'total_users': total_users,
                'total_employees': total_employees,
                'total_employers': total_employers,
            }
        })

def create_job_template(request):
    # Ensure user has employer_id set
    error_response = ensure_employer_id(request)
    if error_response:
        return error_response

    # Filter templates by employer - only fetch necessary fields for template
    templates = JobTemplate.objects.filter(
        employer_id=request.employer_id
    ).only(
        'title',
        'description',
        'updated_at',
        'usage_count'
    ).order_by('-updated_at')

    # Get templates created this month for this employer - optimized query
    now = timezone.now()
    first_day = datetime(now.year, now.month, 1, tzinfo=now.tzinfo)
    templates_this_month = JobTemplate.objects.filter(
        employer_id=request.employer_id,
        created_at__gte=first_day
    ).only('id').count()

    # Calculate total usage for this employer - optimized aggregation
    total_usage = JobTemplate.objects.filter(
        employer_id=request.employer_id
    ).only('usage_count').aggregate(total=Sum('usage_count'))['total'] or 0
    time_saved_percent = 30  # This could be calculated based on real metrics

    context = {
        'templates': templates,
        'templates_this_month': templates_this_month,
        'total_usage': total_usage,
        'time_saved_percent': time_saved_percent
    }

    return render(request, 'create_job_template.html', context)



@permission_required('manage_team')
def manage_permissions(request):
    # Get all employees for the current user's employer - only necessary fields
    try:
        employee = Employee.objects.select_related('employer_id') \
            .only('employer_id__employer_id') \
            .get(user=request.user)
        employer_id = employee.employer_id
        employees = Employee.objects.filter(employer_id=employer_id) \
            .select_related('user') \
            .only(
                'user_id',
                'role',
                'status',
                'created_at',
                'user__first_name',
                'user__last_name',
                'user__email',
                'user__is_active'
            )
    except Employee.DoesNotExist:
        employees = Employee.objects.none()  # No employees found

    # Pagination for team members
    page = request.GET.get('page', 1)
    paginator = Paginator(employees, 30)  # Show 30 users per page

    try:
        team_members = paginator.page(page)
    except PageNotAnInteger:
        team_members = paginator.page(1)
    except EmptyPage:
        team_members = paginator.page(paginator.num_pages)

    # Get all invitations for this employer - only necessary fields for template
    try:
        employee = Employee.objects.select_related('employer_id').get(user=request.user)
        employer_id = employee.employer_id
        invitations = Invitation.objects.filter(employer_id=employer_id).only(
            'id',
            'first_name',
            'last_name',
            'email',
            'role',
            'invitation_status',
            'sent_date',
            'expiry_date',
            'employer_id'
        )
    except Employee.DoesNotExist:
        invitations = Invitation.objects.none()  # No invitations found

    # Pagination for invitations
    inv_page = request.GET.get('inv_page', 1)
    inv_paginator = Paginator(invitations, 30)  # Show 30 invitations per page

    try:
        paginated_invitations = inv_paginator.page(inv_page)
    except PageNotAnInteger:
        paginated_invitations = inv_paginator.page(1)
    except EmptyPage:
        paginated_invitations = inv_paginator.page(inv_paginator.num_pages)

    # Get unique roles, locations, and statuses for filters - only necessary fields
    roles = Employee.objects.only('role').values_list('role', flat=True).distinct()
    locations = OfficeLocation.objects.only('city').values_list('city', flat=True).distinct()
    statuses = ['Active', 'Inactive']  # Assuming these are the statuses

    # Get invitation statuses for invitation tab
    invitation_statuses = ['Pending', 'Accepted', 'Expired', 'Canceled']

    context = {
        'team_members': team_members,
        'invitations': paginated_invitations,
        'total_recruiters': Employee.objects.filter(role='Recruiter', employer_id=employer_id).only('user_id').count(),
        'total_admins': Employee.objects.filter(role='Administrator', employer_id=employer_id).only('user_id').count(),
        'roles': roles,
        'locations': locations,
        'statuses': statuses,
        'invitation_statuses': invitation_statuses,
        'cur_employee': employee
    }

    return render(request, 'manage_permissions.html', context)

def add_portal(request):
    return render(request, 'add_new_job_portal.html')

def manage_preferences(request):
    from django.utils import translation
    from django.http import HttpResponseRedirect
    from django.conf import settings
    from django.contrib import messages
    if request.method == 'POST':
        action = request.POST.get('action')
        if action == 'change_language':
            language = request.POST.get('language')
            print(f"[LANGUAGE-DEBUG] Requested language: {language}")

            # Validate language
            available_languages = [lang[0] for lang in settings.LANGUAGES]
            print(f"[LANGUAGE-DEBUG] Available languages: {available_languages}")

            if language and language in available_languages:
                print(f"[LANGUAGE-DEBUG] Setting language to: {language}")

                # Set language in session using multiple keys for compatibility
                request.session['django_language'] = language
                request.session['_language'] = language
                request.session[settings.LANGUAGE_COOKIE_NAME] = language

                # Force session save
                request.session.modified = True
                request.session.save()

                # Activate the language for this request
                translation.activate(language)

                # Create response with redirect
                response = HttpResponseRedirect(request.path)

                # Set language cookie with multiple approaches
                response.set_cookie(
                    'django_language',
                    language,
                    max_age=60*60*24*365,  # 1 year
                    path='/',
                    secure=False,
                    httponly=False,
                    samesite='Lax'
                )

                # Also set the standard Django language cookie
                response.set_cookie(
                    settings.LANGUAGE_COOKIE_NAME,
                    language,
                    max_age=60*60*24*365,  # 1 year
                    path='/',
                    secure=False,
                    httponly=False,
                    samesite='Lax'
                )

                print(f"[LANGUAGE-DEBUG] Language set successfully: {language}")
                messages.success(request, _("Language changed to %(language)s") % {'language': dict(settings.LANGUAGES)[language]})
                return response
            else:
                print(f"[LANGUAGE-DEBUG] Invalid language: {language}")
                messages.error(request, _("Invalid language selection"))

    # Debug information
    current_language = translation.get_language()
    session_language = request.session.get('django_language', 'Not set')
    cookie_language = request.COOKIES.get('django_language', 'Not set')

    print(f"[LANGUAGE-DEBUG] Current language: {current_language}")
    print(f"[LANGUAGE-DEBUG] Session language: {session_language}")
    print(f"[LANGUAGE-DEBUG] Cookie language: {cookie_language}")

    return render(request, 'job_preferences.html')

def language_test(request):
    """Simple test view to verify language switching works"""
    from django.http import JsonResponse
    from django.utils import translation
    from django.utils.translation import gettext_lazy as _
    from django.conf import settings

    current_language = translation.get_language()
    session_language = request.session.get('django_language', 'Not set')
    cookie_language = request.COOKIES.get('django_language', 'Not set')

    return JsonResponse({
        'current_language': current_language,
        'session_language': session_language,
        'cookie_language': cookie_language,
        'available_languages': [lang[0] for lang in settings.LANGUAGES],
        'test_translation': str(_('Dashboard')),
    })

def middleware_test(request):
    print(f"[MIDDLEWARE-TEST] View reached!")
    print(f"[MIDDLEWARE-TEST] User: {request.user}")
    print(f"[MIDDLEWARE-TEST] Has employer_id: {hasattr(request, 'employer_id')}")
    if hasattr(request, 'employer_id'):
        print(f"[MIDDLEWARE-TEST] employer_id: {request.employer_id}")
    return HttpResponse("Middleware test - check console for debug output")

def get_all_templates(request):
    if request.method == "GET":
        # Ensure user has employer_id set
        error_response = ensure_employer_id_json(request)
        if error_response:
            return error_response

        # Filter templates by employer - only fetch necessary fields for JSON response
        templates = JobTemplate.objects.filter(
            employer_id=request.employer_id
        ).only(
            'title',
            'description',
            'updated_at',
            'usage_count'
        ).order_by('-updated_at')
        templates_list = [
            {
                "title": template.title,
                "description": template.description,
                "updated_at": template.updated_at.strftime("%b %d, %Y"),
                "usage_count": template.usage_count
            }
            for template in templates
        ]
        return JsonResponse({"templates": templates_list})

def get_appointments(request):
    # Ensure user has employer_id set
    error_response = ensure_employer_id_json(request)
    if error_response:
        return error_response

    # Optimize appointments query - only fetch necessary fields for calendar display
    # Filter by employer to ensure data isolation
    appointments = Appointment.objects.select_related('vacancy_id') \
        .filter(vacancy_id__employer_id=request.employer_id) \
        .only(
            'id',
            'title',
            'start_time',
            'end_time',
            'color',
            'interviewers',
            'invited_candidates',
            'appointment_kind',
            'meeting_link',
            'inform_invitees',
            'vacancy_id__vacancy_id',
            'vacancy_id__vacancy_title'
        )
    events = []

    for appt in appointments:
        # Check if start_time and end_time exist before using them
        start_time = appt.start_time.isoformat() if appt.start_time else None
        end_time = appt.end_time.isoformat() if appt.end_time else None

        # Add logging to debug the issue
        if not start_time or not end_time:
            print(f"Warning: Appointment {appt.id} has missing time data: start={appt.start_time}, end={appt.end_time}")

        # Convert vacancy_id to a dictionary or ID instead of using the object directly
        vacancy_data = None
        if appt.vacancy_id:
            vacancy_data = {
                'vacancy_id': appt.vacancy_id.vacancy_id,
                'vacancy_title': appt.vacancy_id.vacancy_title
            }

        candidate_id = appt.invited_candidates[0] if appt.invited_candidates and len(appt.invited_candidates) > 0 else None
        # Optimize candidate query - only fetch necessary fields
        candidate = Candidate.objects.only('candidate_firstname', 'candidate_lastname') \
            .filter(candidate_id=candidate_id).first() if candidate_id else None
        candidate_name = f"{candidate.candidate_firstname} {candidate.candidate_lastname}" if candidate else None

        # Log candidate information for debugging
        print(f"[APPOINTMENT-DEBUG] Appointment {appt.id}: candidate_id={candidate_id}, candidate_name={candidate_name}")

        event = {
            "id": appt.id,
            "title": appt.title,
            "start": start_time,
            "end": end_time,
            "color": appt.color,
            "recruiters": ','.join(appt.interviewers) if appt.interviewers else "",
            "vacancy": vacancy_data,
            "candidate": candidate_id if appt.invited_candidates else None,
            "candidate_name": candidate_name,
            "event_type": appt.appointment_kind,
            "meeting_link": appt.meeting_link or "",
            "inform_invitees": appt.inform_invitees
        }
        events.append(event)

    print(f"[APPOINTMENT-DEBUG] Total events processed: {len(events)}")
    return JsonResponse(events, safe=False)

def get_candidates_for_vacancy(request, vacancy_id=None):
    # Ensure user has employer_id set
    error_response = ensure_employer_id_json(request)
    if error_response:
        return error_response

    vacancy_id = request.GET.get('vacancy')
    candidates = []

    if vacancy_id:
        # First verify that the vacancy belongs to the user's employer
        try:
            vacancy = Vacancy.objects.get(vacancy_id=vacancy_id, employer_id=request.employer_id)
        except Vacancy.DoesNotExist:
            # If vacancy doesn't belong to user's employer, return empty list
            return HttpResponse(render_to_string('partials/candidate_options.html', {'candidates': []}))

        # Get applications for this vacancy - only fetch necessary fields for dropdown
        applications = Application.objects.filter(vacancy_id=vacancy_id) \
            .select_related('candidate_id') \
            .only(
                'candidate_id__candidate_id',
                'candidate_id__candidate_firstname',
                'candidate_id__candidate_lastname'
            )
        candidates = [
            {
                'id': app.candidate_id.candidate_id,
                'name': f"{app.candidate_id.candidate_firstname} {app.candidate_id.candidate_lastname}"
            }
            for app in applications
        ]

    print(candidates)
    # Return HTML for the dropdown options
    return HttpResponse(render_to_string('partials/candidate_options.html', {'candidates': candidates}))

#### FUNCTIONS FOR ADDING DATA FROM HTML TO DB

def send_invitation(request, invitation, employer_name):

    recipient = invitation.email
    subject = f"Invitation: Join {employer_name} at Canvider ATS"
    # Generate a unique invitation link using the token
    invitation_link = f"http://localhost:8000//accept-invitation/{invitation.token}"

    body = f"""
    Dear {invitation.first_name} {invitation.last_name},

    You have been invited to join the {employer_name} team at Canvider ATS as a {invitation.role}.

    To accept this invitation, please click on the following link:
    {invitation_link}

    This invitation will expire on {invitation.expiry_date.strftime('%Y-%m-%d %H:%M:%S')}.

    If you have any questions, please contact the administrator.

    Best regards,
    Canvider ATS Team
    """

    # Send email using redmail
    try:
        r_email = redmail.EmailSender(
            host=SMTP_MAIL_HOST,
            port=SMTP_MAIL_PORT,
            username=MAIL_USERNAME,
            password=MAIL_PASSWORD,
        )
        r_email.connect()
        r_email.send(
            subject=f"{subject}",
            sender=f"{employer_name} <{MAIL_USERNAME}>",
            text=body,
            receivers=recipient
        )
        messages.success(request, _("Invitation mail sent successfully!"))
        return redirect('manage_permissions')
    except Exception as e:
        messages.error(request, _("Failed to send the invitation. Please check the form."))
        return redirect('manage_permissions')


def accept_invitation(request, token):
    invitation = get_object_or_404(Invitation, token=token)

    # Check if invitation is expired or already accepted
    if invitation.invitation_status != 'Pending' or invitation.is_expired():
        invitation.set_expired()
        return render(request, 'register.html', {'invitation': invitation})

    employer_id = invitation.employer_id.employer_id

    # Get employer name
    employer = None
    try:
        employer = Employer.objects.get(employer_id=employer_id)
        employer_name = employer.employer_name
    except Employer.DoesNotExist:
        return render(request, 'register.html', {'invitation': invitation})

    if request.method == 'POST':
        password = request.POST.get('password')
        confirm_password = request.POST.get('confirm_password')

        if password != confirm_password:
            messages.error(request, _("Passwords do not match."))
            return render(request, 'register.html', {
                'invitation': invitation,
                'employer_name': employer_name
            })

        # Create user account
        try:
            user = User.objects.create_user(
                username=invitation.email,
                email=invitation.email,
                password=password,
                first_name=invitation.first_name,
                last_name=invitation.last_name
            )

            if not Employee.objects.filter(user=user).exists():
                employer_obj = None
                if employer:
                    employer_obj = employer
                else:
                    return render(request, 'register.html', {'invitation': invitation})

                if employer_obj:
                    employee = Employee(
                        user=user,
                        role=invitation.role,
                        employer_id=employer_obj
                    )
                    employee.save()
                else:
                    messages.error(request, _("No employer found to associate with this account."))
                    return render(request, 'register.html', {
                        'invitation': invitation,
                        'employer_name': employer_name
                    })

            # Update invitation status
            invitation.invitation_status = 'Accepted'
            invitation.save()

            messages.success(request, _("Registration completed successfully! You can now log in."))
            return redirect('registration_complete')

        except Exception as e:
            messages.error(request, _("Error creating account: %(error)s") % {'error': str(e)})

    return render(request, 'register.html', {
        'invitation': invitation,
        'employer_name': employer_name
    })

def registration_complete(request):
    return render(request, 'registration_complete.html', {
        'login_url': reverse('signin')
    })

def change_invitation_status(request, invitation_id, status):
    # Ensure the invitation belongs to the user's employer
    try:
        employer_id = request.user.employee.employer_id
        invitation = get_object_or_404(Invitation, id=invitation_id, employer_id=employer_id)

        if status == 'Deleted':
            invitation.delete()
            return redirect('manage_permissions')
        else:
            invitation.invitation_status = status
            invitation.save()
            return redirect('manage_permissions')
    except Employee.DoesNotExist:
        messages.error(request, _("Access denied."))
        return redirect('manage_permissions')

@permission_required('manage_team')
def invite_user(request):
    if request.method == 'POST':
        # Print the POST data to see what's being received
        print("POST data:", request.POST)

        form = InvitationForm(request.POST)
        print("Form data:", form.data)
        if form.is_valid():
            # Form is valid, save the invitation
            invitation = form.save(commit=False)
            # Set the required fields
            invitation.employer_id = request.user.employee.employer_id
            invitation.invited_by = request.user
            invitation.save()

            # Get the employer name
            employer_name = Employer.objects.get(employer_id=request.user.employee.employer_id.employer_id).employer_name
            send_invitation(request, invitation, employer_name)

            # Return success response
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({'success': True})
            else:
                messages.success(request, _("Invitation sent successfully!"))
                return redirect('manage_permissions')

        else:
            # Form has errors
            print("Form errors:", form.errors)

            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                # Return detailed error information for AJAX requests
                error_dict = {}
                for field, errors in form.errors.items():
                    error_dict[field] = [str(error) for error in errors]

                return JsonResponse({
                    'success': False,
                    'errors': error_dict
                })
            else:
                messages.error(request, _("Failed to send the invitation. Please check the form."))
                return redirect('manage_permissions')

    # For GET requests
    return redirect('manage_permissions')


@permission_required('manage_team')
def remove_user(request, user_id):
    user = get_object_or_404(Invitation, id=user_id)
    user.delete()
    messages.success(request, _("User removed successfully!"))
    return redirect('invite_user')

def change_employee_status(request, user_id, status):
    user = get_object_or_404(Employee, user_id=user_id)
    user.status = status
    user.save()
    if status == 'Inactive':
        user.user.is_active = False
        user.user.save()
    else:
        user.user.is_active = True
        user.user.save()
    messages.success(request, _("User status changed successfully!"))
    return redirect('manage_permissions')

def increment_template_usage(request):
    if request.method == 'POST':
        # Ensure user has employer_id set
        error_response = ensure_employer_id_json(request)
        if error_response:
            return error_response

        data = json.loads(request.body)
        title = data.get('title')

        try:
            # Filter by employer to ensure data isolation
            template = JobTemplate.objects.get(
                title=title,
                employer_id=request.employer_id
            )
            template.usage_count += 1
            template.save()
            return JsonResponse({'success': True, 'message': 'Template usage count updated.'})
        except JobTemplate.DoesNotExist:
            return JsonResponse({'success': False, 'message': 'Template not found.'})


def get_template(request):
    if request.method == 'POST':
        # Ensure user has employer_id set
        error_response = ensure_employer_id_json(request)
        if error_response:
            return error_response

        data = json.loads(request.body)
        title = data.get('title')

        try:
            # Filter by employer to ensure data isolation
            template = JobTemplate.objects.get(
                title=title,
                employer_id=request.employer_id
            )
            return JsonResponse({
                'success': True,
                'title': template.title,
                'description': template.description,
                'updated_at': template.updated_at.strftime("%b %d, %Y"),
                'usage_count': template.usage_count
            })
        except JobTemplate.DoesNotExist:
            return JsonResponse({'success': False, 'message': 'Template not found.'})



def save_template(request):
    if request.method == 'POST':
        # Ensure user has employer_id set
        error_response = ensure_employer_id_json(request)
        if error_response:
            return error_response

        data = json.loads(request.body)
        title = data.get('title')
        description = data.get('description')

        # Filter by employer and include employer_id and created_by when creating
        template, created = JobTemplate.objects.update_or_create(
            title=title,
            employer_id=request.employer,  # Use the Employer object, not the ID
            defaults={
                'description': description,
                'created_by': request.user
            }
        )

        return JsonResponse({
            'success': True,
            'message': 'Template saved successfully.',
            'updated_at': template.updated_at.strftime("%b %d, %Y")
        })



@permission_required('delete_data')
def delete_template(request):
    if request.method == 'POST':
        # Ensure user has employer_id set
        error_response = ensure_employer_id_json(request)
        if error_response:
            return error_response

        data = json.loads(request.body)
        title = data.get('title')

        try:
            # Filter by employer to ensure data isolation
            template = JobTemplate.objects.get(
                title=title,
                employer_id=request.employer_id
            )
            template.delete()
            return JsonResponse({'success': True, 'message': 'Template deleted successfully.'})
        except JobTemplate.DoesNotExist:
            return JsonResponse({'success': False, 'message': 'Template not found.'})


def insert_comment(request):
    if request.method == 'POST':
        comment_body_input = request.POST.get('comment_body')
        application_id = request.POST.get('application_id')
        application = get_object_or_404(Application, application_id=application_id)

        if len(comment_body_input) > 1:
            comment = ApplicationComment(commented_by=request.user ,comment_body=comment_body_input, application_id=application)
            comment.save()
        else:
            pass

        return redirect('application', application_id=application_id)
    else:
        return HttpResponse("Invalid request method.")


def insert_talent_request(request):
    if request.method == 'POST':
        request_notes = request.POST.get('request_notes')
        vacancy_id = request.POST.get('vacancy_id')
        req_type = request.POST.get('request_type')

        print(req_type)

        vacancy = get_object_or_404(Vacancy, vacancy_id=vacancy_id)
        # get employer using the vacancy
        employer = get_object_or_404(Employer, employer_id=vacancy.employer_id)

        print(vacancy)
        print(employer)

        talent_request = TalentRequest(
            committed_by=request.user,
            request_notes=request_notes,
            vacancy_id=vacancy,
            status="New",
            request_date=timezone.now(),
            employer_id=employer,
            request_type=req_type
            )

        talent_request.save()

        # Send email to the employer
        # message to the user
        messages.success(request, _("Talent request sent successfully! Our team will get back to you soon."))
        # Redirect to the published job details page
        return redirect('details_with_id', vacancy_id=vacancy_id)
    else:
        return HttpResponse(_("Invalid request method."))


@permission_required('make_decisions')
def change_state(request):
    if request.method == 'POST':
        application_id = request.POST.get('application_id')
        state_name = request.POST.get('state_name')
        state_notes = request.POST.get('state_notes')  # Internal notes for recruiters
        email_message = request.POST.get('email_message', '')  # Message for the candidate
        notify_candidate = request.POST.get('notify_candidate') == 'on'
        application = get_object_or_404(Application, application_id=application_id)

        if state_name and state_notes:
            state = ApplicationState(
                state_name=state_name,
                state_notes=state_notes,
                application_id=application,
                committed_by=request.user
            )
            state.save()
            # Update the application state
            application.application_state = state.get_state_name_display()
            application.save()

            # Send email notification if checkbox is checked
            if notify_candidate:
                try:
                    # Get candidate and vacancy information
                    candidate = application.candidate_id
                    vacancy = application.vacancy_id
                    employer = Employer.objects.get(employer_id=vacancy.employer_id)

                    # Create email subject and body
                    subject = f"Your application status has been updated - {vacancy.vacancy_title}"
                    email_subject_key = f"#V{vacancy.vacancy_id}E{vacancy.employer_id}A{application_id}"

                    # Create email body
                    if application.application_state == "Eliminated":
                        application.application_status = "Inactive"
                        application.save()
                        body_lines = [
                            f"<p>Dear {candidate.full_name},</p>",
                            f"<p>We regret to inform you that your application for <strong>{vacancy.vacancy_title}</strong> has not been successful at this time.</p>",
                        ]
                    else:
                        body_lines = [
                            f"<p>Dear {candidate.full_name},</p>",
                            f"<p>We would like to inform you that your application for <strong>{vacancy.vacancy_title}</strong> has been updated to <strong>{application.application_state}</strong>.</p>",
                        ]

                    if email_message:
                        body_lines.append(f" <br> <p>{email_message}</p>")
                    body_lines.append("<br>")
                    body_lines.append("<p>If you have any questions, please don't hesitate to contact us.</p>")
                    body_lines.append(f"<p>Best regards,<br>{employer.employer_name} Recruitment Team</p>")
                    body = "\n".join(body_lines)

                    # Send email using redmail
                    r_email = redmail.EmailSender(
                        host=SMTP_MAIL_HOST,
                        port=SMTP_MAIL_PORT,
                        username=MAIL_USERNAME,
                        password=MAIL_PASSWORD,
                    )
                    r_email.connect()
                    r_email.send(
                        subject=f"{subject} - {email_subject_key}",
                        sender=f"{employer.employer_name} <{MAIL_USERNAME}>",
                        html=body,
                        receivers=candidate.candidate_email
                    )
                    messages.success(request, "Status updated and notification email sent to candidate.")
                except Exception as e:
                    print(f"Error sending status change email: {str(e)}")
                    messages.warning(request, f"Status updated but failed to send email notification: {str(e)}")
            else:
                messages.success(request, "Status updated successfully.")
        else:
            # log error
            print("Error: State was not able to be changed.")
            messages.error(request, "State was not able to be changed.")
            pass

        return redirect('application', application_id=application_id)


def add_appointment(request):
    if request.method == "POST":
        # Ensure user has employer_id set
        error_response = ensure_employer_id_json(request)
        if error_response:
            return error_response

        data = json.loads(request.body)
        title = data.get("title")
        start_time = parse_datetime(data.get("start_time"))
        end_time = parse_datetime(data.get("end_time"))
        recruiters = data.get("recruiters", "")
        vacancy = data.get("vacancy")
        candidate = data.get("candidate")
        event_type = data.get("event_type", "")
        meeting_link = data.get("meeting_link", "")
        inform_invitees = data.get("inform_invitees", False)
        color = data.get("color", 1)

        # Add validation for required fields
        if not title or not start_time or not end_time:
            return JsonResponse({
                "success": False,
                "message": "Missing required fields. Title, start time, and end time are required."
            }, status=400)

        # Validate vacancy belongs to user's employer if provided
        vacancy_obj = None
        if vacancy:
            try:
                vacancy_obj = Vacancy.objects.get(vacancy_id=vacancy, employer_id=request.employer_id)
            except Vacancy.DoesNotExist:
                return JsonResponse({
                    "success": False,
                    "message": "You don't have permission to create appointments for this vacancy."
                }, status=403)

        try:
            appointment = Appointment.objects.create(
                title=title,
                start_time=start_time,
                end_time=end_time,
                appointment_kind=event_type,
                meeting_link=meeting_link,
                inform_invitees=inform_invitees,
                created_by=request.user,
                interviewers=recruiters.split(',') if recruiters else None,
                invited_candidates=[candidate] if candidate else None,
                color=color,
                vacancy_id=vacancy_obj
            )

            if inform_invitees:
                print("Informing invitees")
                inform_invited_people(appointment)

            return JsonResponse({"success": True, "id": appointment.id})
        except Exception as e:
            print(f"Error creating appointment: {str(e)}")
            return JsonResponse({"success": False, "message": str(e)}, status=500)

    return JsonResponse({"success": False, "message": "Invalid request method"}, status=400)

def inform_invited_people(appointment):
    print("Trying to send mail for appointment: ", appointment.title)
    # send the simplest mail to recruiters and invited people
    subject = "Meeting Information"

    candidate = Candidate.objects.get(candidate_id=appointment.invited_candidates[0])
    vacancy = Vacancy.objects.get(vacancy_id=appointment.vacancy_id.vacancy_id)
    employer_name = Employer.objects.get(employer_id=vacancy.employer_id).employer_name
    application = Application.objects.get(vacancy_id=vacancy, candidate_id=candidate)
    email_subject_key = f"#V{appointment.vacancy_id.vacancy_id}E{appointment.vacancy_id.employer_id}A{application.application_id}"

    body = f"""
        <h2>Meeting Invitation</h2>

        <p>Dear {candidate.candidate_firstname} {candidate.candidate_lastname},</p>

        <p>We are pleased to invite you to an upcoming meeting regarding your application for the position of <strong>{vacancy.vacancy_title}</strong>. Please find the details below:</p>

        <table style="border-collapse: collapse; width: 100%; max-width: 600px;">
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;"><strong>Meeting Title:</strong></td>
                <td style="padding: 10px; border: 1px solid #ddd;">{appointment.title}</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;"><strong>Type:</strong></td>
                <td style="padding: 10px; border: 1px solid #ddd;">{appointment.appointment_kind}</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;"><strong>Start Time:</strong></td>
                <td style="padding: 10px; border: 1px solid #ddd;">{appointment.start_time.strftime('%Y-%m-%d %H:%M')}</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;"><strong>End Time:</strong></td>
                <td style="padding: 10px; border: 1px solid #ddd;">{appointment.end_time.strftime('%Y-%m-%d %H:%M')}</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;"><strong>Interviewers:</strong></td>
                <td style="padding: 10px; border: 1px solid #ddd;">{', '.join(appointment.interviewers)}</td>
            </tr>
            {f'<tr><td style="padding: 10px; border: 1px solid #ddd;"><strong>Meeting Link:</strong></td><td style="padding: 10px; border: 1px solid #ddd;"><a href="{appointment.meeting_link}">{appointment.meeting_link}</a></td></tr>' if appointment.meeting_link else ''}
        </table>

        <p>If you have any questions or need to reschedule, please contact your recruiter via email. When doing so, kindly include the following email subject key in your message:</p>

        <p><strong>Email Subject Key:</strong> {email_subject_key}</p>

        <p>We look forward to meeting with you.</p>

        <p>Best regards,<br>{employer_name}</p>
    """

    try:
        r_email = redmail.EmailSender(
            host=SMTP_MAIL_HOST,
            port=SMTP_MAIL_PORT,
            username=MAIL_USERNAME,
            password=MAIL_PASSWORD,
        )
        r_email.connect()
        r_email.send(
            subject=f"{subject} - {email_subject_key}",
            sender=f"{employer_name} <{MAIL_USERNAME}>",
            html=body,
            receivers=candidate.candidate_email,
            cc=appointment.interviewers
        )
        print("Mail sent successfully")
        return True
    except Exception as e:
        print(f"Error sending mail: {str(e)}")
        return False



def update_appointment(request, appointment_id):
    if request.method == "POST":
        # Ensure user has employer_id set
        error_response = ensure_employer_id_json(request)
        if error_response:
            return error_response

        data = json.loads(request.body)
        title = data.get("title")
        start_time = parse_datetime(data.get("start_time"))
        end_time = parse_datetime(data.get("end_time"))
        recruiters = data.get("recruiters", "")
        vacancy = data.get("vacancy")
        candidate = data.get("candidate")
        event_type = data.get("event_type", "")
        meeting_link = data.get("meeting_link", "")
        inform_invitees = data.get("inform_invitees", False)
        color = data.get("color", 1)

        # Get appointment and verify it belongs to user's employer
        appointment = Appointment.objects.filter(
            id=appointment_id,
            vacancy_id__employer_id=request.employer_id
        ).first()

        if not appointment:
            return JsonResponse({
                "success": False,
                "message": "Appointment not found or you don't have permission to update it."
            }, status=403)

        # Validate new vacancy belongs to user's employer if provided
        vacancy_obj = None
        if vacancy:
            try:
                vacancy_obj = Vacancy.objects.get(vacancy_id=vacancy, employer_id=request.employer_id)
            except Vacancy.DoesNotExist:
                return JsonResponse({
                    "success": False,
                    "message": "You don't have permission to assign this vacancy."
                }, status=403)

        appointment.title = title
        appointment.start_time = start_time
        appointment.end_time = end_time
        appointment.appointment_kind = event_type
        appointment.meeting_link = meeting_link
        appointment.color = color
        appointment.vacancy_id = vacancy_obj
        appointment.inform_invitees = inform_invitees
        appointment.interviewers = recruiters.split(',') if recruiters else None
        appointment.invited_candidates = [candidate] if candidate else None
        appointment.save()
        if inform_invitees:
            inform_invited_people(appointment)
        return JsonResponse({"success": True})

    return JsonResponse({"success": False}, status=400)


def delete_appointment(request, appointment_id):
    if request.method == "DELETE":
        # Ensure user has employer_id set
        error_response = ensure_employer_id_json(request)
        if error_response:
            return error_response

        # Get appointment and verify it belongs to user's employer
        appointment = Appointment.objects.filter(
            id=appointment_id,
            vacancy_id__employer_id=request.employer_id
        ).first()

        if not appointment:
            return JsonResponse({
                "success": False,
                "message": "Appointment not found or you don't have permission to delete it."
            }, status=403)

        appointment.delete()
        return JsonResponse({"success": True})

    return JsonResponse({"success": False}, status=400)


def publish_to_linkedin(data, portal_config):
    # TODO
    # Placeholder for LinkedIn publishing logic
    print("Publishing to LinkedIn with data:", data)
    # Implement LinkedIn API call here
    pass

def publish_to_glassdoor(data, portal_config):
    # TODO
    # Placeholder for Glassdoor publishing logic
    print("Publishing to Glassdoor with data:", data)
    # Implement Glassdoor API call here
    pass

@csrf_exempt
@permission_required('create_jobs')
def save_published_job(request):
    if request.method == 'POST':
        # Ensure user has employer_id set
        error_response = ensure_employer_id_json(request)
        if error_response:
            return error_response

        try:
            data = json.loads(request.body)
            Vacancy.objects.create(
                vacancy_title=data.get('roleTitle'),
                vacancy_city=data.get('officeLocation').split(',')[0],
                vacancy_country=data.get('officeLocation').split(',')[1],
                work_schedule=data.get('workSchedule'),
                office_schedule=data.get('officeSchedule'),
                skills=json.dumps(data.get('skills', [])),
                vacancy_job_description=data.get('description'),
                salary_min=data.get('salaryMin'),
                salary_max=data.get('salaryMax'),
                salary_currency=data.get('salaryCurrency'),
                job_portals=json.dumps(data.get('portals', [])),
                jobtags=json.dumps(data.get('benefits', [])),
                employer_id=request.employer_id  # Assign the employer ID for data isolation
            )
            messages.success(request, "Job published to Workloupe successfully!")

            print("Selected portals :", data.get('portals', []))

            for portal in data.get('portals', []):
                if portal == "Linkedin":
                    portal_config = PortalConfigurations.objects.get(portal_name=portal)
                    publish_to_linkedin(data, portal_config)
                elif portal == "Glassdoor":
                    portal_config = PortalConfigurations.objects.get(portal_name=portal)
                    publish_to_glassdoor(data, portal_config)
                else:
                    continue

            return JsonResponse({'success': True})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})
    return JsonResponse({'success': False, 'error': 'Invalid request method'})


@register.filter
def make_float(value):
    try:
        return float(value)
    except (ValueError, TypeError):
        return 0.0

@register.filter
def sub(value, arg):
    try:
        return float(value) - float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def mul(value, arg):
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def div(value, arg):
    try:
        return float(value) / float(arg)
    except (ValueError, TypeError, ZeroDivisionError):
        return 0

@csrf_exempt
def update_vacancy_status(request):
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            vacancy_id = data.get('vacancy_id')
            status = data.get('status')
            vacancy = get_object_or_404(Vacancy, vacancy_id=vacancy_id)
            vacancy.vacancy_status = status
            vacancy.save()
            return JsonResponse({'success': True})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})
    return JsonResponse({'success': False, 'error': 'Invalid request method'})

@csrf_exempt
def departments(request):
    """
    API endpoint for Department CRUD operations
    - GET: Returns all departments for the current user
    - POST: Creates a new department associated with the current user
    """
    if request.method == 'GET':
        try:
            # Get the current user's employee record
            employee = Employee.objects.get(user=request.user)
            employer_id = employee.employer_id

            # Get departments for this employee
            departments = Department.objects.filter(
                employer_id=employer_id
            ).order_by('name')

            departments_list = [
                {
                    "id": dept.id,
                    "name": dept.name,
                    "description": dept.description or "",
                    "created_at": dept.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                    "updated_at": dept.updated_at.strftime("%Y-%m-%d %H:%M:%S")
                }
                for dept in departments
            ]
            return JsonResponse({"departments": departments_list})
        except Employee.DoesNotExist:
            return JsonResponse({
                "success": False,
                "message": "Employee record not found for current user"
            }, status=404)

    elif request.method == 'POST':
        try:
            data = json.loads(request.body)
            name = data.get('name', '').strip()
            description = data.get('description', '').strip()

            if not name:
                return JsonResponse({
                    "success": False,
                    "message": "Department name is required"
                }, status=400)

            # Get the current user's employee record
            employee = Employee.objects.get(user=request.user)

            # Create the department associated with this employee
            department = Department.objects.create(
                employer_id=employee.employer_id,
                name=name,
                description=description
            )

            return JsonResponse({
                "success": True,
                "message": "Department created successfully",
                "department": {
                    "id": department.id,
                    "name": department.name,
                    "description": department.description,
                }
            })
        except Employee.DoesNotExist:
            return JsonResponse({
                "success": False,
                "message": "Employee record not found for current user"
            }, status=404)
        except Exception as e:
            return JsonResponse({
                "success": False,
                "message": str(e)
            }, status=500)

    # Method not allowed
    return JsonResponse({
        "success": False,
        "message": "Method not allowed"
    }, status=405)

@csrf_exempt
def department_detail(request, department_id):
    """
    API endpoint for operations on a specific department
    - GET: Returns details for a specific department
    - PUT: Updates a specific department
    - DELETE: Deletes a specific department
    """
    try:
        # Get the current user's employee record
        employee = Employee.objects.get(user=request.user)
        employer_id = employee.employer_id

        # Get the department and verify it belongs to this employee
        department = Department.objects.get(id=department_id, employee=employee)
    except Employee.DoesNotExist:
        return JsonResponse({
            "success": False,
            "message": "Employee record not found for current user"
        }, status=404)
    except Department.DoesNotExist:
        return JsonResponse({
            "success": False,
            "message": "Department not found or you don't have permission to access it"
        }, status=404)

    if request.method == 'GET':
        return JsonResponse({
            "id": department.id,
            "name": department.name,
            "description": department.description,
            "created_at": department.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "updated_at": department.updated_at.strftime("%Y-%m-%d %H:%M:%S")
        })

    elif request.method == 'PUT':
        try:
            data = json.loads(request.body)
            name = data.get('name', '').strip()
            description = data.get('description', '').strip()

            if not name:
                return JsonResponse({
                    "success": False,
                    "message": "Department name is required"
                }, status=400)

            # Update the department
            department.name = name
            department.description = description
            department.save()

            return JsonResponse({
                "success": True,
                "message": "Department updated successfully",
                "department": {
                    "id": department.id,
                    "name": department.name,
                    "description": department.description,
                }
            })
        except Exception as e:
            return JsonResponse({
                "success": False,
                "message": str(e)
            }, status=500)

    elif request.method == 'DELETE':
        try:
            department.delete()
            return JsonResponse({
                "success": True,
                "message": "Department deleted successfully"
            })
        except Exception as e:
            return JsonResponse({
                "success": False,
                "message": str(e)
            }, status=500)

    # Method not allowed
    return JsonResponse({
        "success": False,
        "message": "Method not allowed"
    }, status=405)

@csrf_exempt
def office_schedules(request):
    """
    API endpoint for Office Schedule CRUD operations
    - GET: Returns all office schedules for the current user
    - POST: Creates a new office schedule associated with the current user
    """
    if request.method == 'GET':
        try:
            # Get the current user's employee record
            employee = Employee.objects.get(user=request.user)
            employer_id = employee.employer_id

            # Get office schedules for this employee
            schedules = OfficeSchedule.objects.filter(employer_id=employer_id).order_by('name')

            schedules_list = [
                {
                    "id": schedule.id,
                    "name": schedule.name,
                    "description": schedule.description or "",
                    "created_at": schedule.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                    "updated_at": schedule.updated_at.strftime("%Y-%m-%d %H:%M:%S")
                }
                for schedule in schedules
            ]
            return JsonResponse({"office_schedules": schedules_list})
        except Employee.DoesNotExist:
            return JsonResponse({
                "success": False,
                "message": "Employee record not found for current user"
            }, status=404)

    elif request.method == 'POST':
        try:
            data = json.loads(request.body)
            name = data.get('name', '').strip()
            description = data.get('description', '').strip()

            if not name:
                return JsonResponse({
                    "success": False,
                    "message": "Office schedule name is required"
                }, status=400)

            # Get the current user's employee record
            employee = Employee.objects.get(user=request.user)

            # Create the office schedule associated with this employee
            schedule = OfficeSchedule.objects.create(
                employer_id=employee.employer_id,
                name=name,
                description=description
            )

            return JsonResponse({
                "success": True,
                "message": "Office schedule created successfully",
                "office_schedule": {
                    "id": schedule.id,
                    "name": schedule.name,
                    "description": schedule.description,
                }
            })
        except Employee.DoesNotExist:
            return JsonResponse({
                "success": False,
                "message": "Employee record not found for current user"
            }, status=404)
        except Exception as e:
            return JsonResponse({
                "success": False,
                "message": str(e)
            }, status=500)

    # Method not allowed
    return JsonResponse({
        "success": False,
        "message": "Method not allowed"
    }, status=405)

@csrf_exempt
def office_schedule_detail(request, schedule_id):
    """
    API endpoint for operations on a specific office schedule
    - GET: Returns details for a specific office schedule
    - PUT: Updates a specific office schedule
    - DELETE: Deletes a specific office schedule
    """
    try:
        # Get the current user's employee record
        employee = Employee.objects.get(user=request.user)
        employer_id = employee.employer_id

        # Get the schedule and verify it belongs to this employee
        schedule = OfficeSchedule.objects.get(id=schedule_id, employer_id=employer_id)
    except Employee.DoesNotExist:
        return JsonResponse({
            "success": False,
            "message": "Employee record not found for current user"
        }, status=404)
    except OfficeSchedule.DoesNotExist:
        return JsonResponse({
            "success": False,
            "message": "Office schedule not found or you don't have permission to access it"
        }, status=404)

    if request.method == 'GET':
        return JsonResponse({
            "id": schedule.id,
            "name": schedule.name,
            "description": schedule.description,
            "created_at": schedule.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "updated_at": schedule.updated_at.strftime("%Y-%m-%d %H:%M:%S")
        })

    elif request.method == 'PUT':
        try:
            data = json.loads(request.body)
            name = data.get('name', '').strip()
            description = data.get('description', '').strip()

            if not name:
                return JsonResponse({
                    "success": False,
                    "message": "Office schedule name is required"
                }, status=400)

            # Update the office schedule
            schedule.name = name
            schedule.description = description
            schedule.save()

            return JsonResponse({
                "success": True,
                "message": "Office schedule updated successfully",
                "office_schedule": {
                    "id": schedule.id,
                    "name": schedule.name,
                    "description": schedule.description,
                }
            })
        except Exception as e:
            return JsonResponse({
                "success": False,
                "message": str(e)
            }, status=500)

    elif request.method == 'DELETE':
        try:
            schedule.delete()
            return JsonResponse({
                "success": True,
                "message": "Office schedule deleted successfully"
            })
        except Exception as e:
            return JsonResponse({
                "success": False,
                "message": str(e)
            }, status=500)

    # Method not allowed
    return JsonResponse({
        "success": False,
        "message": "Method not allowed"
    }, status=405)

@csrf_exempt
def work_schedules(request):
    """
    API endpoint for Work Schedule CRUD operations
    - GET: Returns all work schedules for the current user's employer
    - POST: Creates a new work schedule associated with the current user
    """
    if request.method == 'GET':
        try:
            # Get the current user's employee record
            employee = Employee.objects.get(user=request.user)
            employer_id = employee.employer_id

            # Get work schedules for this user's employer and that are associated with this employee
            schedules = WorkSchedule.objects.filter(employer_id=employer_id).order_by('name')

            schedules_list = [
                {
                    "id": schedule.id,
                    "employer_id": employer_id.employer_id,
                    "name": schedule.name,
                    "description": schedule.description or "",
                    "created_at": schedule.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                    "updated_at": schedule.updated_at.strftime("%Y-%m-%d %H:%M:%S")
                }
                for schedule in schedules
            ]
            return JsonResponse({"work_schedules": schedules_list})
        except Employee.DoesNotExist:
            return JsonResponse({
                "success": False,
                "message": "Employee record not found for current user"
            }, status=404)

    elif request.method == 'POST':
        try:
            data = json.loads(request.body)
            name = data.get('name', '').strip()
            description = data.get('description', '').strip()

            if not name:
                return JsonResponse({
                    "success": False,
                    "message": "Work schedule name is required"
                }, status=400)

            # Get the current user's employee record
            employee = Employee.objects.get(user=request.user)

            # Create the work schedule associated with this employee
            schedule = WorkSchedule.objects.create(
                employer_id=employee.employer_id,
                name=name,
                description=description
            )

            return JsonResponse({
                "success": True,
                "message": "Work schedule created successfully",
                "work_schedule": {
                    "id": schedule.id,
                    "employer_id": employee.employer_id.employer_id,
                    "name": schedule.name,
                    "description": schedule.description,
                }
            })
        except Employee.DoesNotExist:
            return JsonResponse({
                "success": False,
                "message": "Employee record not found for current user"
            }, status=404)
        except Exception as e:
            return JsonResponse({
                "success": False,
                "message": str(e)
            }, status=500)

    # Method not allowed
    return JsonResponse({
        "success": False,
        "message": "Method not allowed"
    }, status=405)

@csrf_exempt
def work_schedule_detail(request, schedule_id):
    """
    API endpoint for operations on a specific work schedule
    - GET: Returns details for a specific work schedule
    - PUT: Updates a specific work schedule
    - DELETE: Deletes a specific work schedule
    """
    try:
        # Get the current user's employee record
        employee = Employee.objects.get(user=request.user)
        employer_id = employee.employer_id

        # Get the schedule and verify it belongs to this employee
        schedule = WorkSchedule.objects.get(id=schedule_id, employer_id=employer_id)
    except Employee.DoesNotExist:
        return JsonResponse({
            "success": False,
            "message": "Employee record not found for current user"
        }, status=404)
    except WorkSchedule.DoesNotExist:
        return JsonResponse({
            "success": False,
            "message": "Work schedule not found or you don't have permission to access it"
        }, status=404)

    if request.method == 'GET':
        return JsonResponse({
            "id": schedule.id,
            "employer_id": employee.employer_id.employer_id,
            "name": schedule.name,
            "description": schedule.description,
            "created_at": schedule.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "updated_at": schedule.updated_at.strftime("%Y-%m-%d %H:%M:%S")
        })

    elif request.method == 'PUT':
        try:
            data = json.loads(request.body)
            name = data.get('name', '').strip()
            description = data.get('description', '').strip()

            if not name:
                return JsonResponse({
                    "success": False,
                    "message": "Work schedule name is required"
                }, status=400)

            # Update the work schedule
            schedule.name = name
            schedule.description = description
            schedule.save()

            return JsonResponse({
                "success": True,
                "message": "Work schedule updated successfully",
                "work_schedule": {
                    "id": schedule.id,
                    "employer_id": employee.employer_id.employer_id,
                    "name": schedule.name,
                    "description": schedule.description,
                }
            })
        except Exception as e:
            return JsonResponse({
                "success": False,
                "message": str(e)
            }, status=500)

    elif request.method == 'DELETE':
        try:
            schedule.delete()
            return JsonResponse({
                "success": True,
                "message": "Work schedule deleted successfully"
            })
        except Exception as e:
            return JsonResponse({
                "success": False,
                "message": str(e)
            }, status=500)

    # Method not allowed
    return JsonResponse({
        "success": False,
        "message": "Method not allowed"
    }, status=405)


def collect_zipcode_from_lat_lon(lat, lon):
    # collect postcode from lat, lon
    url = "https://api.geoapify.com/v1/geocode/reverse?lat=" + str(lat) + "&lon=" + str(lon) + "&apiKey=" + GEO_API_KEY
    response = requests.get(url)
    data = response.json()
    if response.status_code != 200:
        print("Error collecting zipcode:", response.status_code)
        return None

    if data['features']:
        return data['features'][0]['properties']['postcode']
    else:
        return None


def collect_full_address(location_str):
    # collect full address from API
    url = "https://api.geoapify.com/v1/geocode/search?text=" + location_str + "&apiKey=" + GEO_API_KEY
    response = requests.get(url)
    data = response.json()

    if response.status_code != 200:
        print("Error collecting full address:", response.status_code)
        return None

    if data['features']:
        result = data['features'][0]['properties']
        zipcode = collect_zipcode_from_lat_lon(result['lat'], result['lon'])
        print("Zipcode:", zipcode)
        return {
            "city": result['city'],
            "country": result['country'],
            "state": result['state'],
            "postcode": zipcode,
            "lat": result['lat'],
            "lon": result['lon'],
            "formatted": result['formatted']
        }
    else:
        return None

@csrf_exempt
def office_locations(request):
    """
    API endpoint for Office Location CRUD operations
    - GET: Returns all office locations for the current user
    - POST: Creates a new office location associated with the current user
    """
    if request.method == 'GET':
        try:
            # Get the current user's employee record
            employee = Employee.objects.get(user=request.user)
            employer_id = employee.employer_id

            # Get office locations for this employee
            locations = OfficeLocation.objects.filter(
                employer_id=employer_id
            ).order_by('city')

            locations_list = [
                {
                    "id": location.id,
                    "city": location.city,
                    "country": location.country,
                    "created_at": location.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                    "updated_at": location.updated_at.strftime("%Y-%m-%d %H:%M:%S")
                }
                for location in locations
            ]
            return JsonResponse({"office_locations": locations_list})
        except Employee.DoesNotExist:
            return JsonResponse({
                "success": False,
                "message": "Employee record not found for current user"
            }, status=404)

    elif request.method == 'POST':
        try:
            data = json.loads(request.body)
            city = data.get('city', '').strip()
            country = data.get('country', '').strip()
            inserted_address = f"{city}, {country}"
            print("Inserted address:", inserted_address)
            address_details = collect_full_address(inserted_address)

            print("Address details:", address_details)

            if not city:
                return JsonResponse({
                    "success": False,
                    "message": "City is required"
                }, status=400)

            if not country:
                return JsonResponse({
                    "success": False,
                    "message": "Country is required"
                }, status=400)

            # Get the current user's employee record
            employee = Employee.objects.values('employer_id').get(user=request.user)
            print("Employee:", employee)
            print("Employer ID:", employee['employer_id'])

            # Create the office location associated with this employee
            location = OfficeLocation.objects.create(
                employer_id=Employer.objects.get(employer_id=employee['employer_id']),
                city=city,
                country=country,
                location_details=address_details
            )

            return JsonResponse({
                "success": True,
                "message": "Office location created successfully",
                "office_location": {
                    "id": location.id,
                    "city": location.city,
                    "country": location.country
                }
            })
        except Employee.DoesNotExist:
            return JsonResponse({
                "success": False,
                "message": "Employee record not found for current user"
            }, status=404)
        except Exception as e:
            return JsonResponse({
                "success": False,
                "message": str(e)
            }, status=500)

    # Method not allowed
    return JsonResponse({
        "success": False,
        "message": "Method not allowed"
    }, status=405)

@csrf_exempt
def office_location_detail(request, location_id):
    """
    API endpoint for operations on a specific office location
    - GET: Returns details for a specific office location
    - PUT: Updates a specific office location
    - DELETE: Deletes a specific office location
    """
    try:
        # Get the current user's employee record
        employee = Employee.objects.get(user=request.user)
        employer_id = employee.employer_id

        # Get the location and verify it belongs to this employee
        location = OfficeLocation.objects.get(id=location_id, employer_id=employer_id)
    except Employee.DoesNotExist:
        return JsonResponse({
            "success": False,
            "message": "Employee record not found for current user"
        }, status=404)
    except OfficeLocation.DoesNotExist:
        return JsonResponse({
            "success": False,
            "message": "Office location not found or you don't have permission to access it"
        }, status=404)

    if request.method == 'GET':
        return JsonResponse({
            "id": location.id,
            "city": location.city,
            "country": location.country,
            "created_at": location.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "updated_at": location.updated_at.strftime("%Y-%m-%d %H:%M:%S")
        })

    elif request.method == 'PUT':
        try:
            data = json.loads(request.body)
            city = data.get('city', '').strip()
            country = data.get('country', '').strip()

            if not city:
                return JsonResponse({
                    "success": False,
                    "message": "City is required"
                }, status=400)

            if not country:
                return JsonResponse({
                    "success": False,
                    "message": "Country is required"
                }, status=400)

            # Update the office location
            location.city = city
            location.country = country
            location.save()

            return JsonResponse({
                "success": True,
                "message": "Office location updated successfully",
                "office_location": {
                    "id": location.id,
                    "city": location.city,
                    "country": location.country
                }
            })
        except Exception as e:
            return JsonResponse({
                "success": False,
                "message": str(e)
            }, status=500)

    elif request.method == 'DELETE':
        try:
            location.delete()
            return JsonResponse({
                "success": True,
                "message": "Office location deleted successfully"
            })
        except Exception as e:
            return JsonResponse({
                "success": False,
                "message": str(e)
            }, status=500)

    # Method not allowed
    return JsonResponse({
        "success": False,
        "message": "Method not allowed"
    }, status=405)


def all_email_keys_of_employer(employer_id):
    # Get all possible email keys for the given employer
    # email_key = f"#V{vacancy_id}E{employer_id}A{application_id}"
    vac = Vacancy.objects.filter(employer_id=employer_id).values_list('vacancy_id', flat=True)
    appl = Application.objects.filter(vacancy_id__in=vac).values_list('application_id', flat=True)
    email_keys = []
    for application_id in appl:
        # Get the vacancy id for the application
        vacancy_id = Application.objects.get(application_id=application_id).vacancy_id.vacancy_id
        # Create the email key
        email_key = f"#V{vacancy_id}E{employer_id}A{application_id}"
        email_keys.append(email_key)

    return email_keys

def get_emails_notifications(employer_id):
    keys_for_mails = all_email_keys_of_employer(employer_id)
    print("start")
    emails = []
    try:
        with MailBox(MAIL_HOST).login(MAIL_USERNAME, MAIL_PASSWORD) as mailbox:
            mailbox.folder.set('INBOX')
            for key in keys_for_mails:
                print(key)
                try:
                    emails.extend(mailbox.fetch(AND(subject=key)))
                except:
                    continue

        # Sort emails by date
        emails = sorted(emails, key=lambda msg: msg.date.astimezone())
    except:
        print("Error fetching emails from mailbox.")
        emails = None
    print("end")
    return emails


def get_mails_with_key(email_subject_key):
    emails = []
    try:
        with MailBox(MAIL_HOST).login(MAIL_USERNAME, MAIL_PASSWORD) as mailbox:
            # Fetch emails from "Inbox"
            mailbox.folder.set('INBOX')
            emails.extend(mailbox.fetch(AND(subject=email_subject_key)))

            # Fetch emails from "Sent"
            mailbox.folder.set('Sent')
            emails.extend(mailbox.fetch(AND(subject=email_subject_key)))

        # Sort emails by date
        emails = sorted(emails, key=lambda msg: msg.date.astimezone())
    except:
        print("Error fetching emails from mailbox.")
        emails = None

    return emails


def send_email(request):
    if request.method == 'POST':
        data = request.POST

        subject = data.get('subject')
        body = data.get('body')
        email_subject_key = data.get('email_subject_key')
        application_id = email_subject_key.split('A')[1]
        employer_name = data.get('employer_name')
        recipient = data.get('recipent')

        # Send email using redmail
        try:
            r_email = redmail.EmailSender(
                host=SMTP_MAIL_HOST,
                port=SMTP_MAIL_PORT,
                username=MAIL_USERNAME,
                password=MAIL_PASSWORD,
            )
            r_email.connect()
            r_email.send(
                subject=f"{subject} - {email_subject_key}",
                sender=f"{employer_name} <{MAIL_USERNAME}>",
                text=body,
                receivers=recipient
            )
            messages.success(request, "Mail has been sent successfully, it will appear on this page after a few minutes.")
            return redirect('application', application_id=application_id)
        except Exception as e:
            messages.error(request, f"Failed to send email: {str(e)}")
            return redirect('application', application_id=application_id)

def get_all_comments_from_last_days(days=7, employer_id=None, exclude_user_id=None):
    # Get the current date
    current_date = timezone.now().date()
    # Calculate the start date
    start_date = current_date - timedelta(days=days)

    # Fetch comments from the last 'days' days, filtered by employer
    comments_query = ApplicationComment.objects.filter(comment_date__gte=start_date)

    # Filter by employer if provided - only show comments on applications for this employer's jobs
    if employer_id:
        comments_query = comments_query.filter(application_id__vacancy_id__employer_id=employer_id)

    # Exclude comments made by the current user (so they don't see their own comments in activity feed)
    if exclude_user_id:
        comments_query = comments_query.exclude(commented_by_id=exclude_user_id)

    comments = comments_query.order_by('-comment_date').values(
        'application_id', 'comment_date', 'commented_by_id'
    )
    return comments

@permission_required('manage_candidates')
def send_bulk_mails(request):
    """
    Send bulk emails to applicants based on their application status.
    This function processes the request asynchronously, allowing the user to continue working.
    """
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': 'Invalid request method'}, status=400)

    # Get form data
    vacancy_id = request.POST.get('vacancy_id')
    application_status = request.POST.get('application_status')
    email_subject = request.POST.get('email_subject')
    email_body = request.POST.get('email_body')
    internal_notes = request.POST.get('internal_notes', '')
    notify_candidates = request.POST.get('notify_candidates') == 'on'

    # Validate required fields
    if not all([vacancy_id, application_status, email_subject, email_body]):
        return JsonResponse({'success': False, 'message': 'Missing required fields'}, status=400)

    try:
        # Get the vacancy - only fetch necessary fields
        vacancy = Vacancy.objects.only('vacancy_id', 'employer_id', 'vacancy_title') \
            .get(vacancy_id=vacancy_id)

        # Get the employer - only fetch necessary fields
        employer = Employer.objects.only('employer_id', 'employer_name') \
            .get(employer_id=vacancy.employer_id)

        # Get applications with the specified status - optimize with select_related
        applications = Application.objects.filter(
            vacancy_id=vacancy,
            application_state=application_status
        ).select_related('candidate_id', 'vacancy_id') \
        .only(
            'application_id',
            'application_state',
            'candidate_id__candidate_firstname',
            'candidate_id__candidate_email',
            'vacancy_id__vacancy_id',
            'vacancy_id__vacancy_title'
        )

        if not applications.exists():
            return JsonResponse({
                'success': False,
                'message': f'No applicants found with status: {application_status}'
            }, status=404)

        # Start a background thread to send emails
        thread = threading.Thread(
            target=send_bulk_emails_background,
            args=(applications, email_subject, email_body, employer, internal_notes, notify_candidates, request.user)
        )
        thread.daemon = True
        thread.start()

        return JsonResponse({
            'success': True,
            'message': f'Sending emails to {applications.count()} applicants with status: {application_status}',
            'count': applications.count()
        })

    except Vacancy.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'Vacancy not found'}, status=404)
    except Employer.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'Employer not found'}, status=404)
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'Error: {str(e)}'}, status=500)

def send_bulk_emails_background(applications, email_subject, email_body, employer, internal_notes, notify_candidates, user):
    """
    Background function to send emails to multiple applicants.
    This runs in a separate thread to avoid blocking the main request.
    """
    try:
        # Get email configuration
        SMTP_MAIL_HOST = os.getenv("SMTP_MAIL_HOST")
        SMTP_MAIL_PORT = int(os.getenv("SMTP_MAIL_PORT"))
        MAIL_USERNAME = os.getenv("MAIL_USERNAME")
        MAIL_PASSWORD = os.getenv("MAIL_PASSWORD")

        # Create email sender
        email_sender = redmail.EmailSender(
            host=SMTP_MAIL_HOST,
            port=SMTP_MAIL_PORT,
            username=MAIL_USERNAME,
            password=MAIL_PASSWORD,
        )
        email_sender.connect()

        # Process each application
        for application in applications:
            candidate = application.candidate_id
            vacancy = application.vacancy_id

            # Create a comment for internal tracking
            if internal_notes:
                comment = ApplicationComment(
                    application_id=application,
                    commented_by=user,
                    comment_body=f"Bulk email sent. Internal notes: {internal_notes}"
                )
                comment.save()

            # Skip sending email if notify_candidates is False
            if not notify_candidates:
                continue

            # Prepare email content
            email_subject_key = f"#V{vacancy.vacancy_id}E{employer.employer_id}A{application.application_id}"

            # Build email body with HTML formatting
            body_lines = []
            body_lines.append(f"<p>Dear {candidate.candidate_firstname},</p>")
            body_lines.append("<br>")
            body_lines.append(f"<p>{email_body}</p>")
            body_lines.append("<br>")
            body_lines.append("<p>If you have any questions, please don't hesitate to contact us.</p>")
            body_lines.append(f"<p>Best regards,<br>{employer.employer_name} Recruitment Team</p>")
            body = "\n".join(body_lines)

            # Send the email
            email_sender.send(
                subject=f"{email_subject} - {email_subject_key}",
                sender=f"{employer.employer_name} <{MAIL_USERNAME}>",
                html=body,
                receivers=candidate.candidate_email
            )

            # Add a small delay to avoid overwhelming the email server
            time.sleep(0.5)

    except Exception as e:
        print(f"Error in bulk email background task: {str(e)}")

def collect_related_mails():
    pass



def extract_text_from_pdf(file_path):
    text = ""
    try:
        # First try with PyPDF
        reader = PdfReader(file_path)
        for page in reader.pages:
            page_text = page.extract_text()
            text += page_text if page_text else ""

        # If PyPDF returned empty or very little text, try OCR
        if len(text.strip()) < 50:  # Adjust threshold as needed
            print("PyPDF extraction yielded little text, trying OCR...", flush=True)
            images = pdf2image.convert_from_path(file_path)
            ocr_text = ""
            for img in images:
                ocr_text += pytesseract.image_to_string(img)

            # Use OCR result if it's better than PyPDF
            if len(ocr_text.strip()) > len(text.strip()):
                text = ocr_text
    except Exception as e:
        print(f"Error extracting text from PDF: {str(e)}", flush=True)

    return text



def applicant_states_over_time(lookback_days=10, employer_id=None):
    # Get the last 10 days
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=(lookback_days - 1))

    # Use raw SQL for the complex query with date series and employer filtering
    with connection.cursor() as cursor:  # Ensure the cursor is properly managed
        if employer_id:
            cursor.execute("""
                WITH
                  date_series AS (
                    SELECT
                      generate_series(%s::date, %s::date, '1 day'::interval)::date AS day
                  ),
                  application_states AS (
                    SELECT
                      sc.application_id,
                      date_trunc('day', sc.state_started_at)::date AS day,
                      CASE
                        WHEN sc.state_name IN ('New') THEN 'New'
                        WHEN sc.state_name IN ('In-Review', 'Review #1', 'Review #2', 'Review #3', 'Review #4', 'Review #5', 'Review_1', 'Review_2', 'Review_3', 'Review_4', 'Review_5') THEN 'In-Review'
                        WHEN sc.state_name IN ('Ready for Decision', 'Decision', 'Offer Made', 'Candidate Accepted', 'Hired', 'Offer', 'Accept', 'Final Interview', 'Background Check') THEN 'Final Stage'
                        WHEN sc.state_name IN ('Candidate Rejected', 'Eliminated', 'Reject', 'Rejected', 'Not Selected', 'Declined') THEN 'Rejected'
                      END AS state_name
                    FROM
                      feed_applicationstate sc
                      JOIN feed_application app ON sc.application_id = app.application_id
                      JOIN feed_vacancy vac ON app.vacancy_id = vac.vacancy_id
                    WHERE vac.employer_id = %s::varchar
                      AND sc.state_started_at >= %s::date
                  ),
                  daily_counts AS (
                    SELECT
                      application_states.day,
                      application_states.state_name,
                      COUNT(DISTINCT application_states.application_id) AS count
                    FROM
                      application_states
                    GROUP BY
                      application_states.day,
                      application_states.state_name
                  )
                SELECT
                  d.day,
                  s.state_name,
                  COALESCE(dc.count, 0) AS count
                FROM
                  date_series d
                  CROSS JOIN (VALUES ('New'), ('Rejected'), ('In-Review'), ('Final Stages')) AS s(state_name)
                  LEFT JOIN daily_counts dc ON d.day = dc.day AND s.state_name = dc.state_name
                ORDER BY
                  d.day, s.state_name;
            """, [start_date, end_date, employer_id, start_date])
        else:
            # Fallback to original query if no employer_id provided
            cursor.execute("""
                WITH
                  date_series AS (
                    SELECT
                      generate_series(%s::date, %s::date, '1 day'::interval)::date AS day
                  ),
                  application_states AS (
                    SELECT
                      sc.application_id,
                      d.day,
                      CASE
                        WHEN sc.state_name IN ('New') THEN 'New'
                        WHEN sc.state_name IN ('Review_1', 'Review_2', 'Review_3', 'Review_4', 'Review_5') THEN 'In-Review'
                        WHEN sc.state_name IN ('Decision', 'Offer', 'Accept', 'Hired') THEN 'Final Stages'
                        WHEN sc.state_name IN ('Reject', 'Eliminated') THEN 'Rejected'
                      END AS state_name
                    FROM
                      feed_applicationstate sc
                      JOIN date_series d ON date_trunc('day', sc.state_started_at) <= d.day
                  ),
                  daily_counts AS (
                    SELECT
                      application_states.day,
                      application_states.state_name,
                      COUNT(DISTINCT application_states.application_id) AS count
                    FROM
                      application_states
                    GROUP BY
                      application_states.day,
                      application_states.state_name
                  )
                SELECT
                  d.day,
                  s.state_name,
                  COALESCE(dc.count, 0) AS count
                FROM
                  date_series d
                  CROSS JOIN (VALUES ('New'), ('Rejected'), ('In-Review'), ('Final Stage')) AS s(state_name)
                  LEFT JOIN daily_counts dc ON d.day = dc.day AND s.state_name = dc.state_name
                ORDER BY
                  d.day, s.state_name;
            """, [start_date, end_date])

        columns = [col[0] for col in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]

    # Process results after the cursor block
    all_unique_dates = set(row['day'] for row in results)
    formatted_unique_dates = sorted(all_unique_dates)
    all_unique_dates = [date.strftime("%Y-%m-%d") for date in formatted_unique_dates]

    # Create dictionaries for easier lookup
    results_by_day_state = {}
    for row in results:
        key = (row['day'], row['state_name'])
        results_by_day_state[key] = row['count']

    # Ensure all arrays have the same length as dates array
    num_days = len(formatted_unique_dates)
    new_counts = []
    in_review_counts = []
    final_stages_counts = []
    rejected_counts = []

    for date in formatted_unique_dates:
        new_counts.append(results_by_day_state.get((date, 'New'), 0))
        in_review_counts.append(results_by_day_state.get((date, 'In-Review'), 0))
        final_stages_counts.append(results_by_day_state.get((date, 'Final Stage'), 0))
        rejected_counts.append(results_by_day_state.get((date, 'Rejected'), 0))



    return all_unique_dates, new_counts, in_review_counts, final_stages_counts, rejected_counts

def get_overview_totals(employer_id=None):
    with connection.cursor() as cursor:
        if employer_id:
            cursor.execute("""
                SELECT COUNT(app.application_id) AS count,
                    CASE WHEN app.application_state IN ('New') THEN 'New'
                    WHEN app.application_state IN ('In-Review', 'Review #1', 'Review #2', 'Review #3', 'Review #4', 'Review #5', 'Review_1', 'Review_2', 'Review_3', 'Review_4', 'Review_5') THEN 'In-Review'
                    WHEN app.application_state IN ('Ready for Decision', 'Decision', 'Offer Made', 'Candidate Accepted', 'Hired', 'Offer', 'Accept', 'Final Interview', 'Background Check') THEN 'Final Stage'
                    WHEN app.application_state IN ('Candidate Rejected', 'Eliminated', 'Reject', 'Rejected', 'Not Selected', 'Declined') THEN 'Rejected'
                  END AS state_name
                FROM feed_application app
                JOIN feed_vacancy vac ON app.vacancy_id = vac.vacancy_id
                WHERE vac.employer_id = %s::varchar
                GROUP BY state_name
                ORDER BY count DESC;
            """, [employer_id])
        else:
            # Fallback to original query if no employer_id provided
            cursor.execute("""
                SELECT COUNT(application_id) AS count,
                    CASE WHEN application_state IN ('New') THEN 'New'
                    WHEN application_state IN ('In-Review', 'Review #1', 'Review #2', 'Review #3', 'Review #4', 'Review #5') THEN 'In-Review'
                    WHEN application_state IN ('Ready for Decision', 'Decision', 'Offer Made', 'Candidate Accepted', 'Hired') THEN 'Final Stage'
                    WHEN application_state IN ('Candidate Rejected', 'Eliminated') THEN 'Rejected'
                  END AS state_name
                FROM feed_application
                GROUP BY state_name
                ORDER BY count DESC;
            """)

        columns = [col[0] for col in cursor.description]
        overview_totals = [dict(zip(columns, row)) for row in cursor.fetchall()]

    return overview_totals


def get_applicants_from_mailbox():
    """
    Legacy function - now redirects to the new management command.
    Use: python manage.py process_postjobfree_applications
    """
    from django.core.management import call_command

    try:
        call_command('process_postjobfree_applications', limit=10)
        print("PostJobFree applications processed successfully")
    except Exception as e:
        print(f"Error processing PostJobFree applications: {str(e)}")

def process_postjobfree_applications_manual(user=None):
    """
    Manual trigger for PostJobFree application processing with confirmation emails.
    This function can be called from views or other parts of the application.
    """
    print("=== Starting PostJobFree Application Processing ===")

    from feed.utils.pdf_utils import extract_text_from_pdf, validate_pdf_content
    from feed.utils.application_parser import PostJobFreeParser
    from canviderAi.services import extract_candidate_info_from_cv
    from datetime import datetime, timedelta

    parser = PostJobFreeParser()
    processed_count = 0

    print(f"Email configuration: Host={MAIL_HOST}, Username={MAIL_USERNAME}")

    # Find emails that were already processed to avoid duplicates
    processed_emails = set(ApplicationState.objects.filter(
        state_notes__contains='PostJobFree.com',
        state_started_at__gte=timezone.now() - timedelta(days=30)  # Last 30 days
    ).values_list('application_id__candidate_id__candidate_email', flat=True))

    print(f"Found {len(processed_emails)} already processed applications in last 30 days")

    # Find the latest ApplicationState with PostJobFree note
    latest_postjobfree_state = ApplicationState.objects.filter(
        state_notes='Candidate applied from PostJobFree.com'
    ).only('state_started_at').order_by('-state_started_at').first()

    if latest_postjobfree_state:
        since_date = latest_postjobfree_state.state_started_at
        print(f"📅 Processing emails since last PostJobFree application: {since_date}")
    else:
        since_date = None
        print("📅 No previous PostJobFree applications found, processing all emails")

    try:
        print("Connecting to mailbox...")
        with MailBox(MAIL_HOST).login(MAIL_USERNAME, MAIL_PASSWORD) as mailbox:
            print("Successfully connected to mailbox")
            mailbox.folder.set('INBOX')

            # Search for PostJobFree emails
            if since_date:
                since_date_for_search = since_date.date()
                criteria = AND(from_="<EMAIL>", date_gte=since_date_for_search)
            else:
                criteria = AND(from_="<EMAIL>")

            all_messages = list(mailbox.fetch(criteria, mark_seen=False, limit=20))

            # Filter messages that have attachments
            messages = []
            for msg in all_messages:
                if msg.attachments and len(msg.attachments) > 0:
                    messages.append(msg)

            print(f"Found {len(all_messages)} total PostJobFree emails, {len(messages)} with attachments to process")

            for i, msg in enumerate(messages, 1):
                print(f"\n--- Processing email {i}/{len(messages)} ---")
                try:
                    # Parse email information
                    subject_info = parser.parse_email_subject(msg.subject)
                    body_info = parser.parse_email_body(msg.text or msg.html or "")
                    combined_info = {**subject_info, **body_info}

                    candidate_email = combined_info.get('candidate_email')
                    if not candidate_email:
                        print("❌ No candidate email found")
                        continue

                    # Skip if already processed recently
                    if candidate_email in processed_emails:
                        print(f"⏭️ Skipping already processed application for {candidate_email}")
                        continue

                    # Process PDF attachment
                    pdf_content = None
                    for attachment in msg.attachments:
                        if attachment.filename.lower().endswith('.pdf'):
                            pdf_content = attachment.payload
                            break

                    if not pdf_content or not validate_pdf_content(pdf_content):
                        print("❌ Invalid or missing PDF")
                        continue

                    # Extract and process resume
                    resume_text = extract_text_from_pdf(pdf_content)
                    if not resume_text:
                        print("❌ Could not extract text from PDF")
                        continue

                    candidate_info = extract_candidate_info_from_cv(resume_text)

                    # Find matching vacancy
                    vacancy = None
                    if combined_info.get('vacancy_id'):
                        vacancy = _find_vacancy_by_id(combined_info['vacancy_id'])
                    if not vacancy and combined_info.get('job_title'):
                        vacancy = _find_matching_vacancy(combined_info['job_title'])
                    if not vacancy:
                        print("❌ No matching vacancy found")
                        continue

                    # Create application and send confirmation
                    success = _create_postjobfree_application(
                        combined_info, candidate_info, vacancy, pdf_content, resume_text, user=user
                    )

                    if success:
                        processed_count += 1
                        processed_emails.add(candidate_email)
                        mailbox.flag([msg.uid], ['\\Seen'], True)
                        # Send confirmation email
                        try:
                            employer = Employer.objects.get(employer_id=vacancy.employer_id)
                            confirmation_subject = f"Application Received - {vacancy.vacancy_title}"
                            confirmation_body = f"""
                                Dear {candidate_info.get('first_name', 'Candidate')},

                                Thank you for applying to the {vacancy.vacancy_title} position at {employer.employer_name}.
                                We have received your application and will review it shortly.

                                Your application will be carefully reviewed by our recruitment team, and we will contact you
                                if your qualifications match our requirements.

                                Best regards,
                                {employer.employer_name} Recruitment Team
                            """

                            r_email = redmail.EmailSender(
                                host=SMTP_MAIL_HOST,
                                port=SMTP_MAIL_PORT,
                                username=MAIL_USERNAME,
                                password=MAIL_PASSWORD
                            )
                            r_email.connect()  # Add connection before sending
                            r_email.send(
                                subject=confirmation_subject,
                                sender=f"{employer.employer_name} <{MAIL_USERNAME}>",
                                text=confirmation_body,
                                receivers=[candidate_email]
                            )
                            print(f"✅ Sent confirmation email to {candidate_email}")
                        except Exception as e:
                            print(f"⚠️ Failed to send confirmation email: {str(e)}")

                except Exception as e:
                    print(f"❌ Error processing email: {str(e)}")
                    traceback.print_exc()
                    continue

    except Exception as e:
        print(f"❌ Mailbox connection failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return 0

    print(f"\n=== Processing Complete: {processed_count} applications created ===")
    return processed_count

def _find_vacancy_by_id(vacancy_id):
    """Find vacancy by exact ID."""
    if not vacancy_id:
        return None

    try:
        vacancy = Vacancy.objects.filter(
            vacancy_id=vacancy_id,
            vacancy_status='Active'
        ).only('vacancy_id', 'vacancy_title', 'employer_id') \
        .first()
        return vacancy
    except Exception as e:
        print(f"Error finding vacancy by ID {vacancy_id}: {str(e)}")
        return None

def _find_matching_vacancy(job_title):
    """Find matching vacancy based on job title."""
    if not job_title:
        return None

    # Try exact match first - only fetch necessary fields
    vacancy = Vacancy.objects.filter(
        vacancy_title__iexact=job_title,
        vacancy_status='Active'
    ).only('vacancy_id', 'vacancy_title', 'employer_id') \
    .first()

    if vacancy:
        return vacancy

    # Try partial match - only fetch necessary fields
    job_title_words = job_title.lower().split()
    for word in job_title_words:
        if len(word) > 3:  # Skip short words
            vacancy = Vacancy.objects.filter(
                vacancy_title__icontains=word,
                vacancy_status='Active'
            ).only('vacancy_id', 'vacancy_title', 'employer_id') \
            .first()
            if vacancy:
                return vacancy

    return None

def _create_postjobfree_application(email_info, candidate_info, vacancy, pdf_content, resume_text, user=None):
    """Create candidate and application records for PostJobFree applications."""
    try:
        from django.db import transaction
        from django.contrib.auth.models import User

        with transaction.atomic():
            # Create or get candidate - only fetch necessary fields
            candidate_email = email_info['candidate_email']
            # Get all candidates with this email and order by most recent
            existing_candidates = Candidate.objects.filter(
                candidate_email=candidate_email
            ).only('candidate_id', 'candidate_email', 'candidate_firstname', 'candidate_lastname') \
            .order_by('-candidate_id')

            if existing_candidates.exists():
                # Use the most recent candidate record
                candidate = existing_candidates.first()
            else:
                # Create new candidate if none exists
                candidate = Candidate.objects.create(
                    candidate_email=candidate_email,
                    candidate_firstname=candidate_info.get('first_name', 'Unknown'),
                    candidate_lastname=candidate_info.get('last_name', 'Unknown'),
                    candidate_phone=candidate_info.get('phone', ''),
                    candidate_address=candidate_info.get('location', ''),
                    candidate_date_of_birth="1111-01-01"
                )

            # Check if application already exists - only fetch necessary field
            existing_application = Application.objects.filter(
                candidate_id=candidate,
                vacancy_id=vacancy
            ).only('application_id').first()

            if existing_application:
                print(f"Application already exists for {candidate_email} -> {vacancy.vacancy_title}")
                return True

            # Upload CV to S3
            cv_filename = secure_filename(f"{candidate_email}_{vacancy.vacancy_id}.pdf")
            cv_filename = f"{os.urandom(8).hex()}_{cv_filename}"

            import io
            cv_file_obj = io.BytesIO(pdf_content)
            s3_client_cv.upload_file(cv_file_obj, cv_filename)

            # Create application
            application = Application.objects.create(
                candidate_id=candidate,
                vacancy_id=vacancy,
                application_source='PostJobFree',
                application_status='Active',
                application_state='New',
                education_level=candidate_info.get('education_level', 'Unknown'),
                current_position=candidate_info.get('current_position', 'Unknown'),
                current_employer=candidate_info.get('current_employer', 'Unknown'),
                total_exp_years=candidate_info.get('total_experience_years', 0.0) or 0.0,
                cv_location=cv_filename,
                score=-1
            )

            # Create CV text record
            ApplicationCvText.objects.create(
                application_id=application,
                cv_text=resume_text,
                cv_location=cv_filename,
                is_cv_analyzed=False,
                ai_analysis_result=None
            )

            # Create journey information (ApplicationState) for PostJobFree applications
            # Use the provided user or get a system user
            if user is None:
                # Try to get a system user or the first admin user
                user = User.objects.filter(is_staff=True).first()
                if user is None:
                    # Fallback to any user if no admin exists
                    user = User.objects.first()

            if user:
                ApplicationState.objects.create(
                    state_name='New',
                    state_notes='Candidate applied from PostJobFree.com',
                    application_id=application,
                    committed_by=user
                )
                print(f"Created journey information for application {application.application_id}")

            print(f"Successfully created application for {candidate_info.get('first_name', 'Unknown')} {candidate_info.get('last_name', 'Unknown')} -> {vacancy.vacancy_title}")
            return True

    except Exception as e:
        print(f"Failed to create application: {str(e)}")
        return False

@csrf_exempt
def trigger_postjobfree_processing(request):
    """
    Manual trigger endpoint for PostJobFree application processing.
    """
    print(f"=== API Endpoint Called ===")
    print(f"Method: {request.method}")
    print(f"Headers: {dict(request.headers)}")
    print(f"User: {request.user}")

    if request.method != 'POST':
        print("❌ Invalid method - only POST allowed")
        return JsonResponse({'success': False, 'message': 'Only POST method allowed'}, status=405)

    try:
        print("🚀 Starting PostJobFree processing...")
        processed_count = process_postjobfree_applications_manual(user=request.user if request.user.is_authenticated else None)

        response_data = {
            'success': True,
            'message': f'Successfully processed {processed_count} PostJobFree applications',
            'processed_count': processed_count
        }
        print(f"✅ API Success: {response_data}")
        return JsonResponse(response_data)

    except Exception as e:
        error_message = f'Error processing applications: {str(e)}'
        print(f"❌ API Error: {error_message}")
        import traceback
        traceback.print_exc()

        return JsonResponse({
            'success': False,
            'message': error_message
        }, status=500)


def careers_page(request):
    """Main careers page setup interface"""
    current_employee = Employee.objects.get(user=request.user)
    current_employer_id = current_employee.employer_id.employer_id
    current_employer_name = Employer.objects.get(employer_id=current_employer_id).employer_name
    return render(request, 'careers_page.html', {'current_employer_name': current_employer_name})


@login_required
def create_careers_page_full(request):
    """Full careers page builder interface"""
    try:
        current_employee = Employee.objects.get(user=request.user)
        current_employer = current_employee.employer_id

        context = {
            'current_employer': current_employer,
        }
        return render(request, 'create_careers_page_full.html', context)
    except Employee.DoesNotExist:
        return render(request, 'create_careers_page_full.html')


def create_careers_widget(request):
    """Careers widget builder interface"""
    try:
        current_employee = Employee.objects.get(user=request.user)
        current_employer = current_employee.employer_id

        # Get active vacancies for preview
        active_vacancies = Vacancy.objects.filter(
            employer_id=current_employer.employer_id,
            vacancy_status='Active'
        ).order_by('-vacancy_creation_date')[:5]

        context = {
            'employer': current_employer,
            'active_vacancies': active_vacancies
        }
        return render(request, 'create_careers_widget.html', context)
    except Employee.DoesNotExist:
        return render(request, 'create_careers_widget.html')


def wordpress_integration(request):
    """WordPress integration interface"""
    return render(request, 'wordpress_integration.html')


def workloupe_platform(request):
    """Workloupe platform profile builder interface"""
    context = {}

    if request.user.is_authenticated:
        try:

            current_employee = Employee.objects.get(user=request.user)
            current_employer = current_employee.employer_id
            existing_gallery_images = s3_client_photos.list_files(f"company-galleries/{current_employer.employer_id}/")[1:]
            try:
                context['existing_gallery_images'] = [f"{AWS_ENDPOINT_URL}/canvider-public/{image}" for image in existing_gallery_images]
                print(f"[WORKLOUPE-DEBUG] Found {len(existing_gallery_images)} existing gallery images", flush=True)
                print(f"[WORKLOUPE-DEBUG] Gallery images: {context['existing_gallery_images']}", flush=True)
            except:
                print("[WORKLOUPE-DEBUG] Error parsing existing gallery images, defaulting to empty list", flush=True)
                context['existing_gallery_images'] = []
            context['employer'] = current_employer
        except Employee.DoesNotExist:
            pass


    return render(request, 'workloupe_platform.html', context)

@csrf_exempt
def create_workloupe_profile(request):
    """API endpoint to create/update company profile on Workloupe platform"""
    print("[WORKLOUPE-DEBUG] Starting profile creation/update request", flush=True)
    
    if request.method != 'POST':
        print("[WORKLOUPE-DEBUG] Invalid request method", flush=True)
        return JsonResponse({'success': False, 'error': 'Invalid request method'})

    if not request.user.is_authenticated:
        print("[WORKLOUPE-DEBUG] Unauthenticated request", flush=True)
        return JsonResponse({'success': False, 'error': 'Authentication required'})

    try:
        print("[WORKLOUPE-DEBUG] Getting employer info", flush=True)
        employee = Employee.objects.get(user=request.user)
        employer = employee.employer_id

        print(f"[WORKLOUPE-DEBUG] Processing employer: {employer.employer_name}", flush=True)

        # print entire form data
        print(f"[WORKLOUPE-DEBUG] Form data: {request.POST}", flush=True)

        post_data_dict = request.POST.dict()

        print(f"[WORKLOUPE-DEBUG] Form data dict: {post_data_dict}", flush=True)

        # employer email 
        employer_email = post_data_dict['employer_email']
        if employer_email:
            print(f"[WORKLOUPE-DEBUG] Employer email: {employer_email}", flush=True)
        else:
            print("[WORKLOUPE-DEBUG] No employer email provided", flush=True)

        # Update employer fields from form data
        employer.employer_name = request.POST.get('employer_name', employer.employer_name)
        employer.employer_email = request.POST.get('employer_email', employer.employer_email)
        employer.employer_phone = request.POST.get('employer_phone', employer.employer_phone)
        employer.employer_website = request.POST.get('employer_website', employer.employer_website)
        employer.employer_address = request.POST.get('employer_address', employer.employer_address)
        employer.office_locations = request.POST.get('office_locations', employer.office_locations)
        employer.employer_description = request.POST.get('employer_description', employer.employer_description)
        employer.employer_industry = request.POST.get('employer_industry', employer.employer_industry)
        try:
            employer.employer_headcount = request.POST.get('employer_headcount', employer.employer_headcount)
        except:
            employer.employer_headcount = 0
        #employer.employer_social_portals = request.POST.get('employer_social_portals', employer.employer_social_portals)
        print("[WORKLOUPE-DEBUG] Basic info updated", flush=True)
        # Convert social portals string to dict from str
        social_portals = request.POST.get('employer_social_portals', employer.employer_social_portals)
        if social_portals:
            social_portals = json.loads(social_portals)
            print(f"[WORKLOUPE-DEBUG] Social portals: {social_portals}", flush=True)
            if 'social_links' in social_portals:
                social_portals = social_portals['social_links']
                social_res = ""
                for s in social_portals:
                    social_res = social_res + s + ","
                social_portals = social_res
            print(f"[WORKLOUPE-DEBUG] Social portals after parsing: {social_portals}", flush=True)
        else:
            social_portals = {}
            print("[WORKLOUPE-DEBUG] No social portals provided", flush=True)

        
        # Handle file uploads to Digital Ocean Spaces
        if 'logo' in request.FILES:
            print("[WORKLOUPE-DEBUG] Processing logo upload", flush=True)
            logo_file = request.FILES['logo']
            print(f"[WORKLOUPE-DEBUG] Logo file name: {logo_file.name}", flush=True)
            # example https://canvider-public.fra1.cdn.digitaloceanspaces.com/offline_logos/ergungrouplogo.jpg
            # get the extension of the image file.
            logo_extension = logo_file.name.split('.')[-1]
            logo_file_name = f"offline_logos/{employer.employer_id}_logo.{logo_extension}"
            s3_client_photos.upload_file(logo_file, logo_file_name, is_public=True)
            print(f"[WORKLOUPE-DEBUG] Logo uploaded successfully: {logo_file_name}", flush=True)
            employer.employer_logo_url = f"{AWS_ENDPOINT_URL}/canvider-public/{logo_file_name}"
            print(f"[WORKLOUPE-DEBUG] Logo URL updated: {employer.employer_logo_url}", flush=True) 

        if 'banner' in request.FILES:
            print("[WORKLOUPE-DEBUG] Processing banner upload", flush=True)
            banner_file = request.FILES['banner']
            print(f"[WORKLOUPE-DEBUG] Banner file name: {banner_file.name}", flush=True)
            # example https://canvider-public.fra1.cdn.digitaloceanspaces.com/offline_banners/ergungroupbanner.jpg
            # get the extension of the image file.
            banner_extension = banner_file.name.split('.')[-1]
            banner_file_name = f"offline_banners/{employer.employer_id}_banner.{banner_extension}"
            s3_client_photos.upload_file(banner_file, banner_file_name, is_public=True)
            print(f"[WORKLOUPE-DEBUG] Banner file uploaded: {banner_file_name}", flush=True)
            employer.employer_banner_url = f"{AWS_ENDPOINT_URL}/canvider-public/{banner_file_name}"
            print(f"[WORKLOUPE-DEBUG] Banner uploaded successfully: {banner_file_name}", flush=True)

        # Handle gallery images
        print("[WORKLOUPE-DEBUG] Processing gallery images", flush=True)
        gallery_urls = []
        gallery_files = request.FILES.getlist('gallery[]')
        for i, gallery_file in enumerate(gallery_files):
            print(f"[WORKLOUPE-DEBUG] Processing gallery image {i+1}/{len(gallery_files)}", flush=True)
            # example https://canvider-public.fra1.cdn.digitaloceanspaces.com/company-galleries/1/IMG_9367.jpeg
            gallery_file_name = f"company-galleries/{employer.employer_id}/{gallery_file.name}"
            s3_client_photos.upload_file(gallery_file, gallery_file_name, is_public=True)
            gallery_urls.append(gallery_file_name)

        # Handle existing gallery images
        existing_gallery_images = s3_client_photos.list_files(f"company-galleries/{employer.employer_id}/")
        
        try:
            print(f"[WORKLOUPE-DEBUG] Found {len(existing_gallery_images)} existing gallery images", flush=True)
        except:
            print("[WORKLOUPE-DEBUG] Error parsing existing gallery images, defaulting to empty list", flush=True)
            existing_gallery_images = []

        # Combine existing and new gallery images
        all_gallery_images = existing_gallery_images + gallery_urls
        print(f"[WORKLOUPE-DEBUG] Total gallery images: {len(all_gallery_images)}", flush=True)

        # Limit to 50 photos
        if len(all_gallery_images) > 50:
            print("[WORKLOUPE-DEBUG] Truncating gallery to 50 images", flush=True)
            all_gallery_images = all_gallery_images[:50]
        
        # Parse existing social data or create new structure
        print("[WORKLOUPE-DEBUG] Overwriting social data", flush=True)
        employer.employer_social_portals = social_portals

        
        print("[WORKLOUPE-DEBUG] Saving employer profile", flush=True)
        employer.save()

        # Generate company slug for URL
        company_slug = employer.employer_name.lower().replace(' ', '-').replace('&', 'and')
        company_slug = ''.join(c for c in company_slug if c.isalnum() or c == '-')
        print(f"[WORKLOUPE-DEBUG] Generated company slug: {company_slug}", flush=True)

        print("[WORKLOUPE-DEBUG] Profile creation/update completed successfully", flush=True)
        return JsonResponse({
            'success': True,
            'company_slug': company_slug,
            'message': 'Profile created successfully'
        })

    except Employee.DoesNotExist:
        print("[WORKLOUPE-DEBUG] Employee not found error", flush=True)
        return JsonResponse({'success': False, 'error': 'Employee not found'})
    except Exception as e:
        print(f"[WORKLOUPE-DEBUG] Unexpected error: {str(e)}", flush=True)
        return JsonResponse({'success': False, 'error': str(e)})

@csrf_exempt
def remove_gallery_image(request):
    """Remove a gallery image from S3 storage"""
    if request.method == 'POST':
        try:
            # Parse request data
            data = json.loads(request.body)
            image_url = data.get('image_url')
            employer_id = data.get('employer_id')

            # Validate required fields
            if not image_url or not employer_id:
                return JsonResponse({
                    'success': False, 
                    'error': _('Image URL and employer ID are required')
                })

            # Extract image path from URL and construct S3 key
            image_name = image_url.split('/')[-1]
            image_key = f"company-galleries/{employer_id}/{image_name}"

            print(f"[GALLERY-DEBUG] Removing image: {image_key}", flush=True)

            # Delete image from S3
            s3_client_photos.delete_file(image_key)

            # Update employer's social portals data to remove image reference
            try:
                employer = Employer.objects.get(employer_id=employer_id)
                social_data = json.loads(employer.employer_social_portals or '{}')
                if 'gallery_images' in social_data:
                    social_data['gallery_images'] = [
                        img for img in social_data['gallery_images'] 
                        if image_name not in img
                    ]
                    employer.employer_social_portals = json.dumps(social_data)
                    employer.save()
            except Exception as e:
                print(f"[GALLERY-DEBUG] Error updating employer data: {str(e)}", flush=True)
                # Continue even if employer update fails - image was still deleted

            return JsonResponse({'success': True})
        except Exception as e:
            print(f"[GALLERY-DEBUG] Error removing image: {str(e)}", flush=True)
            return JsonResponse({
                'success': False, 
                'error': _('Failed to remove image: %(error)s') % {'error': str(e)}
            })

    return JsonResponse({
        'success': False,
        'error': _('Invalid request method')
    }, status=405)

@csrf_exempt
def generate_careers_page_HTML(request):
    """Generate careers page HTML based on the provided configuration input"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Invalid request method'})
        
    try:
        # First validate that we have JSON data
        if not request.body:
            return JsonResponse({'success': False, 'error': 'No configuration data provided'})
            
        try:
            config = json.loads(request.body)
        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'error': 'Invalid JSON data'})

        # Validate required fields from frontend
        required_fields = ['companyName', 'tagline', 'secondTitle', 'secondtagline', 'primaryColor', 'secondaryColor']
        for field in required_fields:
            if field not in config:
                return JsonResponse({'success': False, 'error': f'Missing required field: {field}'})

        # Get current employer from the auth user
        current_employee = Employee.objects.get(user=request.user)
        current_employer = current_employee.employer_id
        current_employer_name = current_employer.employer_name

        # Use provided RSS feed URL or generate default one
        rss_feed_url = config.get('rssFeedUrl') or f"https://workloupe.com/company/{current_employer_name}/jobs.rss"

        print(rss_feed_url)
        print(current_employer_name)

        # Generate HTML based on configuration
        if config.get('isWidget', False):
            html_content = generate_widget_html(
                company_name=config['companyName'],
                tagline=config['tagline'],
                logo_url=config.get('logoUrl', ''),
                banner_url=config.get('bannerUrl', ''),
                rss_feed_url=rss_feed_url,
                second_title=config['secondTitle'],
                second_tagline=config['secondtagline'],
                primary_color=config['primaryColor'],
                secondary_color=config['secondaryColor'],
                max_jobs=config.get('maxJobs', 5),
                show_salary=config.get('showSalary', True),
                show_location=config.get('showLocation', True),
                show_date=config.get('showDate', True)
            )
        else:
            html_content = generate_full_page_html(
                company_name=config['companyName'],
                tagline=config['tagline'],
                logo_url=config.get('logoUrl', ''),
                banner_url=config.get('bannerUrl', ''),
                rss_feed_url=rss_feed_url,
                second_title=config['secondTitle'],
                second_tagline=config['secondtagline'],
                primary_color=config['primaryColor'],
                secondary_color=config['secondaryColor'],
                enable_search=config.get('enableSearch', False),
                is_full_page=config.get('isFullPage', True),
                is_widget=config.get('isWidget', False)
            )

        return JsonResponse({
            'success': True, 
            'html': html_content
        })

    except Employee.DoesNotExist:
        return JsonResponse({
            'success': False, 
            'error': 'Employee record not found'
        })
    except Exception as e:
        return JsonResponse({
            'success': False, 
            'error': str(e)
        })


def generate_widget_html(company_name, tagline, logo_url, banner_url, rss_feed_url, second_title, second_tagline, primary_color, secondary_color, max_jobs, show_salary, show_location, show_date):
    """Generate HTML for careers widget"""
    # Implement widget HTML generation logic here

    rss_feed_url = '"' + rss_feed_url + '"'
    banner_url = '"' + banner_url + '"'

    result = """
        
      <style>
        :root {
          --primary-color: """ + primary_color +""";
          --secondary-color: """ + secondary_color +""";
          --body-bg: rgb(238, 238, 238);
          --text-color: #111111;
          --text-secondary: #474747;
          --body-bg-light: rgb(249, 249, 249);
        }

        @media (prefers-color-scheme: dark) {
          :root {
            --primary-color: """ + primary_color +""";
            --secondary-color: """ + secondary_color +""";
            --body-bg: #121212;
            --text-color: #ffffff;
            --text-secondary: #a0a0a0;
            --body-bg-light: #1e1e1e;
          }
        }

        .min-vh-100 {
          min-height: 100vh;
        }

        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
          line-height: 1.6;
          color: var(--text-color);
          background: var(--body-bg);
        }

        .header {
          background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)),
            url(""" + banner_url +""");
          background-size: cover;
          background-position: center;
          color: white;
          padding: 80px 20px;
          text-align: center;
        }

        .header h1 {
          font-size: 2.5rem;
          font-weight: 700;
          margin-bottom: 20px;
        }
        .header p {
          font-size: 1.25rem;
          max-width: 700px;
          margin: 0 auto;
          opacity: 0.9;
        }
        .header img {
          max-height: 80px;
          margin-bottom: 20px;
        }

        .jobs-section {
          padding: 60px 20px;
          background: var(--body-bg);
        }
        .jobs-container {
          max-width: 1200px;
          margin: 0 auto;
        }
        .jobs-header {
          text-align: center;
          margin-bottom: 40px;
        }
        .jobs-header h2 {
          font-size: 2rem;
          font-weight: 700;
          color: var(--text-color);
          margin-bottom: 15px;
        }
        .jobs-header p {
          font-size: 1.1rem;
          color: var(--text-secondary);
          max-width: 700px;
          margin: 0 auto 30px;
        }

        .search-container {
          max-width: 600px;
          margin: 0 auto 40px;
          position: relative;
        }
        .search-input {
          width: 100%;
          padding: 14px 20px;
          border: 2px solid var(--text-secondary) !important;
          border-radius: 50px;
          font-size: 1rem;
          background: var(--body-bg-light);
          color: var(--text-color);
        }
        .search-btn {
          position: absolute;
          right: 8px;
          top: 50%;
          transform: translateY(-50%);
          background: var(--primary-color);
          color: var(--body-bg-light);
          border: none;
          padding: 8px 20px;
          border-radius: 50px;
          cursor: pointer;
        }

        .jobs-grid {
          display: grid;
          gap: 24px;
        }
        .job-card {
          background: var(--body-bg-light);
          padding: 24px;
          border-radius: 12px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          border: 4px solid transparent !important;
          transition: 0.3s ease-in;
          margin-bottom: 10px;
          margin-top: 10px;
        }
        .job-card:hover {
          box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
          border: 4px solid var(--secondary-color) !important;
          margin-top: -10px;
        }
        .job-card:hover .apply-btn {
          background: var(--secondary-color) !important;
        }
        .job-card:hover .job-title {
          color: var(--secondary-color) !important;
        }

        .job-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 15px;
        }
        .job-title {
          font-size: 1.25rem;
          font-weight: 700;
          color: var(--primary-color);
          margin: 0;
        }
        .job-type {
          background: rgba(108, 117, 125, 0.1);
          color: var(--text-color);
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 0.85rem;
        }
        .job-location,
        .job-salary {
          color: var(--text-secondary);
          margin: 0 0 10px 0;
          font-size: 0.95rem;
          display: flex;
          align-items: center;
        }
        .job-date {
          color: var(--text-secondary);
          margin: 0 0 10px 0;
          font-size: 0.95rem;
          display: flex;
          align-items: center;
        }

        .job-location i,
        .job-date i,
        .job-salary i {
          margin-right: 8px;
          width: 20px;
        }

        .job-description {
          color: var(--text-secondary);
          margin: 0 0 20px 0;
          line-height: 1.6;
          font-size: 0.95rem;
        }
        .apply-btn {
          background: var(--primary-color);
          color: var(--body-bg-light);
          text-decoration: none;
          padding: 10px 24px;
          border-radius: 6px;
          font-weight: 600;
          display: inline-block;
          transition: 0.3s ease;
        }
        .apply-btn:hover {
          background: var(--secondary-color);
          color: var(--body-bg-light);
        }

        .job-body {
          max-height: 100px !important;
          overflow-y: hidden !important;
        }

        @media (max-width: 768px) {
          .header h1 {
            font-size: 2rem;
          }
          .jobs-header h2 {
            font-size: 1.75rem;
          }
          .job-header {
            flex-direction: column;
            align-items: flex-start;
          }
          .job-type {
            margin-top: 10px;
          }
        }
      </style>

      <div class="workloupe-widget" style="margin-top: 20px;">
        <!-- RSS Feed Scraper to run when the page loads -->
        <script>
          async function fetchRSSFeed() {
            const rssFeedUrl = """+ rss_feed_url +""";
            const proxyUrl = "https://api.allorigins.win/get?url=";

            try {
              const response = await fetch(
                proxyUrl + encodeURIComponent(rssFeedUrl)
              );
              const data = await response.json();
              const xmlText = data.contents;
              const parser = new DOMParser();
              const xmlDoc = parser.parseFromString(xmlText, "text/xml");

              const jobs = xmlDoc.getElementsByTagName("job");
              /// prepare the container div to have multiple cards on responsive mode
              let output = '<div class="container"><div class="row g-4">';

              for (let i = 0; i < jobs.length; i++) {
                const job = jobs[i];
                const title = job.getElementsByTagName("title")[0]?.textContent || 'Position Not Specified';
                const date = job.getElementsByTagName("date")[0]?.textContent || new Date().toISOString();
                const company = job.getElementsByTagName("company")[0]?.textContent || 'Company Not Specified';
                const city = job.getElementsByTagName("city")[0]?.textContent || 'City Not Specified';
                const country = job.getElementsByTagName("country")[0]?.textContent || 'Country Not Specified';
                const description = job.getElementsByTagName("description")[0]?.textContent || 'No description available';
                const url = job.getElementsByTagName("url")[0]?.textContent || '#';
                const salary = job.getElementsByTagName("salary")[0]?.textContent || 'Salary Not Specified';
                
                // Format the date to be more readable with fallback
                let published_date;
                try {
                    const rawDate = job.getElementsByTagName("date")[0]?.textContent || new Date().toISOString();
                    const dateObj = new Date(rawDate);
                    published_date = dateObj.toLocaleDateString('en-US', { 
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                    });
                } catch (e) {
                    published_date = 'Date Not Available';
                }
                
                const employment_type = job.getElementsByTagName("employment_type")[0]?.textContent || 'Full-time';

                output += `
            <div class="col-12 col-sm-12 col-lg-4 col-md-6">
              <div class="job-card h-100">
                <div class="job-header">
                  <h3 class="job-title">${title}</h3>
                  <div class="job-type">${employment_type}</div>
                </div>
                <p class="job-location"><i class="fas fa-map-marker-alt"></i> ${city}, ${country}</p>
                <p class="job-salary"><i class="fas fa-dollar-sign"></i> ${salary}</p>
                <p class="job-date"><i class="fas fa-calendar"></i> Posted on ${published_date}</p>
                <div class="job-footer">
                  <a href="${url}" class="btn apply-btn w-100 mt-2" target="_blank">Learn more & Apply</a>
                </div>
              </div>
            </div>
          `;
              }
              output += "</div></div>";

              document.getElementById("rssFeedContainer").innerHTML = output;
            } catch (error) {
              console.error("Error fetching the RSS feed:", error);
              document.getElementById("rssFeedContainer").innerHTML =
                '<p class="text-center">Error loading the RSS feed. Please refresh the page or ensure the quality of the RSS Configuration.</p>';
            }
          }

          // Call the function to fetch and display the RSS feed
          window.addEventListener("load", function () {
            fetchRSSFeed();
          });
        </script>

        <div id="rssFeedContainer"> </div>
      </div>


    """

    return result


@csrf_exempt
def generate_wordpress_package(request):
    """Generate WordPress package with careers folder structure"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Invalid request method'})

    try:
        # First validate that we have JSON data
        if not request.body:
            return JsonResponse({'success': False, 'error': 'No configuration data provided'})

        try:
            config = json.loads(request.body)
        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'error': 'Invalid JSON data'})

        # Validate required fields from frontend
        required_fields = ['companyName', 'tagline', 'secondTitle', 'secondtagline', 'primaryColor', 'secondaryColor']
        for field in required_fields:
            if field not in config:
                return JsonResponse({'success': False, 'error': f'Missing required field: {field}'})

        # Get current employer from the auth user
        current_employee = Employee.objects.get(user=request.user)
        current_employer = current_employee.employer_id
        current_employer_name = current_employer.employer_name

        # Use provided RSS feed URL or generate default one
        rss_feed_url = config.get('rssFeedUrl') or f"https://workloupe.com/company/{current_employer_name}/jobs.rss"

        # Generate full page HTML for WordPress
        html_content = generate_full_page_html(
            company_name=config['companyName'],
            tagline=config['tagline'],
            logo_url=config.get('logoUrl', ''),
            banner_url=config.get('bannerUrl', ''),
            rss_feed_url=rss_feed_url,
            second_title=config['secondTitle'],
            second_tagline=config['secondtagline'],
            primary_color=config['primaryColor'],
            secondary_color=config['secondaryColor'],
            enable_search=config.get('enableSearch', False),
            is_full_page=True,
            is_widget=False
        )


        # Create a simple response with the HTML content
        # The frontend will handle creating the folder structure
        return JsonResponse({
            'success': True,
            'html': html_content,
            'filename': 'careers/index.html'
        })

    except Employee.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': 'Employee record not found'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


def generate_full_page_html(company_name, tagline, logo_url, banner_url, rss_feed_url, second_title, second_tagline, primary_color, secondary_color, enable_search, is_full_page, is_widget):
    """Generate HTML for full careers page"""
    # Implement full page HTML generation logic here
    widget_part = generate_widget_html(
        company_name=company_name,
        tagline=tagline,
        logo_url=logo_url,
        banner_url=banner_url,
        rss_feed_url=rss_feed_url,
        second_title=second_title,
        second_tagline=second_tagline,
        primary_color=primary_color,
        secondary_color=secondary_color,
        max_jobs=5,
        show_salary=True,
        show_location=True,
        show_date=True
    )

    result = """

<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>""" + company_name + """ - Careers</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC"
      crossorigin="anonymous"
    />
  </head>
  <body>
    <header class="header">
      <img
        src=""" + logo_url +"""
        alt="Company Logo"
      />
      <h1>""" + company_name +"""</h1>
      <p>""" + tagline +"""</p>
    </header>

    <section class="jobs-section">
      <div class="jobs-container">
        <div class="jobs-header">
          <h2>""" + second_title + """</h2>
          <p>""" + second_tagline + """</p>

          <div class="search-container">
            <input
              type="text"
              class="search-input"
              placeholder="Search by Keywords or Locations..."
            />
          </div>
        </div>
      </div>
    </section>

    <div id="rssFeedContainerParent">
        """ + widget_part + """
    </div>
    
    <div class="footer text-center p-3 mt-5 bg-light">
        <p class="text-dark">Powered by Canvider</p>
    </div>

    <!-- Search Functionality -->
    <script>
      // Wait for both DOM and RSS feed to load
      const searchHandler = () => {
        const searchInput = document.querySelector(".search-input");
        if (!searchInput) return;

        const performSearch = () => {
          const query = searchInput.value.toLowerCase().trim();
          const jobCards = document.querySelectorAll(".job-card");

          jobCards.forEach((card) => {
            try {
              const title = (
                card.querySelector(".job-title")?.textContent || ""
              ).toLowerCase();
              const location = (
                card.querySelector(".job-location")?.textContent || ""
              ).toLowerCase();

              // Show all cards if search is empty
              if (query === "") {
                card.style.display = "block";
                return;
              }

              // Search in available content
              const shouldShow =
                title.includes(query) || location.includes(query);
              card.style.display = shouldShow ? "block" : "none";
            } catch (err) {
              console.error("Error processing card:", err);
              card.style.display = "block"; // Show card on error
            }
          });
        };

        // Debounce search to improve performance
        let searchTimeout;
        searchInput.addEventListener("input", () => {
          clearTimeout(searchTimeout);
          searchTimeout = setTimeout(performSearch, 300);
        });
      };

      // Initialize search when DOM loads
      document.addEventListener("DOMContentLoaded", searchHandler);

      // Re-initialize search when RSS feed updates
      const observer = new MutationObserver(searchHandler);
      observer.observe(document.getElementById("rssFeedContainer"), {
        childList: true,
        subtree: true,
      });
    </script>
  </body>
</html>
<!DOCTYPE html>

    
    """
    return result


def debug_auth(request):
    """Debug view to check authentication and employee setup"""
    # Manually set employer_id if middleware didn't do it (for testing)
    if request.user.is_authenticated and not hasattr(request, 'employer_id'):
        try:
            employee = request.user.employee
            if employee.status == 'Active':
                request.employer_id = employee.employer_id.employer_id
                request.employer = employee.employer_id
                request.employee = employee
        except AttributeError:
            pass

    debug_info = {
        'user_authenticated': request.user.is_authenticated,
        'user_id': request.user.id if request.user.is_authenticated else None,
        'username': request.user.username if request.user.is_authenticated else None,
        'has_middleware_employer_id': hasattr(request, 'employer_id'),
        'middleware_employer_id': getattr(request, 'employer_id', None),
        'request_path': request.path,
    }

    if request.user.is_authenticated:
        try:
            employee = request.user.employee
            debug_info.update({
                'has_employee_record': True,
                'employee_status': employee.status,
                'employer_id': employee.employer_id.employer_id,
                'employer_name': employee.employer_id.employer_name,
                'employee_role': employee.role,
            })
        except AttributeError as e:
            debug_info.update({
                'has_employee_record': False,
                'error': str(e),
                'error_type': 'AttributeError - User has no employee record'
            })

            # Check if there are any Employee records for this user
            employee_count = Employee.objects.filter(user=request.user).count()
            debug_info['employee_records_count'] = employee_count

        except Exception as e:
            debug_info.update({
                'has_employee_record': False,
                'error': str(e),
                'error_type': type(e).__name__
            })

    return JsonResponse(debug_info, json_dumps_params={'indent': 2})


def clear_activity_feed(request):
    """Clear all activity feed notifications for the current user's employer"""
    print(f"[CLEAR-DEBUG] Clear activity feed called, method: {request.method}")

    if request.method == 'POST':
        # Ensure user has employer_id set
        error_response = ensure_employer_id(request)
        if error_response:
            print(f"[CLEAR-DEBUG] Authentication failed")
            return JsonResponse({'success': False, 'error': 'Authentication required'})

        print(f"[CLEAR-DEBUG] User authenticated, employer_id: {request.employer_id}")

        try:
            # Store the timestamp when user cleared their activity feed in session
            from django.utils import timezone

            session_key = f'activity_cleared_{request.employer_id}_{request.user.id}'
            request.session[session_key] = timezone.now().isoformat()

            print(f"[CLEAR-DEBUG] Activity feed cleared successfully for employer {request.employer_id}, user {request.user.id}")
            return JsonResponse({'success': True, 'message': 'Activity feed cleared successfully'})

        except Exception as e:
            print(f"[CLEAR-DEBUG] Error clearing activity feed: {str(e)}")
            return JsonResponse({'success': False, 'error': str(e)})

    print(f"[CLEAR-DEBUG] Invalid request method: {request.method}")
    return JsonResponse({'success': False, 'error': 'Invalid request method'})
