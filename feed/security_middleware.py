"""
Security middleware for additional security headers and protections
"""
import logging
from django.http import HttpResponseForbidden
from django.conf import settings
from django.utils.deprecation import MiddlewareMixin
from django.core.cache import cache
import time

logger = logging.getLogger(__name__)


class SecurityHeadersMiddleware(MiddlewareMixin):
    """
    Middleware to add security headers to all responses
    """
    
    def process_response(self, request, response):
        # Content Security Policy
        response['Content-Security-Policy'] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; "
            "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; "
            "img-src 'self' data: https: blob:; "
            "font-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; "
            "connect-src 'self' https:; "
            "frame-ancestors 'none'; "
            "base-uri 'self'; "
            "form-action 'self';"
        )
        
        # Additional security headers
        response['Permissions-Policy'] = (
            "geolocation=(), microphone=(), camera=(), "
            "payment=(), usb=(), magnetometer=(), gyroscope=()"
        )
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        response['Cross-Origin-Embedder-Policy'] = 'require-corp'
        response['Cross-Origin-Opener-Policy'] = 'same-origin'
        
        return response


class RateLimitMiddleware(MiddlewareMixin):
    """
    Simple rate limiting middleware for login attempts and API calls
    """
    
    def process_request(self, request):
        # Rate limit login attempts
        if request.path == '/signin/' and request.method == 'POST':
            client_ip = self.get_client_ip(request)
            cache_key = f"login_attempts_{client_ip}"
            attempts = cache.get(cache_key, 0)
            
            if attempts >= 5:  # Max 5 attempts per hour
                logger.warning(f"Rate limit exceeded for IP {client_ip} on login")
                return HttpResponseForbidden("Too many login attempts. Please try again later.")
            
            # Increment attempts counter
            cache.set(cache_key, attempts + 1, 3600)  # 1 hour timeout
        
        # Rate limit API endpoints
        if request.path.startswith('/api/') and request.method == 'POST':
            client_ip = self.get_client_ip(request)
            cache_key = f"api_requests_{client_ip}"
            requests = cache.get(cache_key, 0)
            
            if requests >= 100:  # Max 100 API requests per hour
                logger.warning(f"API rate limit exceeded for IP {client_ip}")
                return HttpResponseForbidden("API rate limit exceeded. Please try again later.")
            
            cache.set(cache_key, requests + 1, 3600)  # 1 hour timeout
        
        return None
    
    def get_client_ip(self, request):
        """Get the client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class InputSanitizationMiddleware(MiddlewareMixin):
    """
    Middleware to sanitize and validate input data
    """
    
    DANGEROUS_PATTERNS = [
        '<script',
        'javascript:',
        'vbscript:',
        'onload=',
        'onerror=',
        'onclick=',
        'onmouseover=',
        'onfocus=',
        'onblur=',
        'onchange=',
        'onsubmit=',
        'eval(',
        'expression(',
        'url(',
        'import(',
    ]
    
    def process_request(self, request):
        # Sanitize POST data
        if request.method == 'POST' and hasattr(request, 'POST'):
            for key, value in request.POST.items():
                if isinstance(value, str):
                    if self.contains_dangerous_content(value):
                        logger.warning(f"Dangerous content detected in POST data: {key}")
                        return HttpResponseForbidden("Invalid input detected.")
        
        # Sanitize GET parameters
        if request.method == 'GET' and hasattr(request, 'GET'):
            for key, value in request.GET.items():
                if isinstance(value, str):
                    if self.contains_dangerous_content(value):
                        logger.warning(f"Dangerous content detected in GET data: {key}")
                        return HttpResponseForbidden("Invalid input detected.")
        
        return None
    
    def contains_dangerous_content(self, content):
        """Check if content contains dangerous patterns"""
        content_lower = content.lower()
        for pattern in self.DANGEROUS_PATTERNS:
            if pattern in content_lower:
                return True
        return False
