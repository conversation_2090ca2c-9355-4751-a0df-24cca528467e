"""
Custom template filters for secure HTML rendering
"""
from django import template
from django.utils.safestring import mark_safe
from django.utils.html import strip_tags, escape
import bleach
import re

register = template.Library()

# Allowed HTML tags for email content (safe subset)
ALLOWED_TAGS = [
    'p', 'br', 'strong', 'b', 'em', 'i', 'u', 'span', 'div',
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'ul', 'ol', 'li',
    'a', 'img',
    'table', 'tr', 'td', 'th', 'thead', 'tbody',
    'blockquote', 'pre', 'code'
]

# Allowed attributes for HTML tags
ALLOWED_ATTRIBUTES = {
    'a': ['href', 'title'],
    'img': ['src', 'alt', 'width', 'height'],
    'span': ['style'],
    'div': ['style'],
    'p': ['style'],
    'table': ['style', 'border', 'cellpadding', 'cellspacing'],
    'td': ['style', 'colspan', 'rowspan'],
    'th': ['style', 'colspan', 'rowspan'],
}

# Allowed CSS properties (for style attributes)
ALLOWED_STYLES = [
    'color', 'background-color', 'font-size', 'font-weight', 'font-family',
    'text-align', 'text-decoration', 'margin', 'padding',
    'border', 'border-color', 'border-width', 'border-style',
    'width', 'height', 'max-width', 'max-height'
]

@register.filter
def safe_html(value):
    """
    Safely render HTML content by sanitizing it with bleach
    Removes dangerous tags and attributes while preserving safe formatting
    """
    if not value:
        return ''
    
    # Clean the HTML with bleach
    cleaned_html = bleach.clean(
        value,
        tags=ALLOWED_TAGS,
        attributes=ALLOWED_ATTRIBUTES,
        styles=ALLOWED_STYLES,
        strip=True,  # Strip disallowed tags instead of escaping
        strip_comments=True
    )
    
    # Additional security: remove any remaining script-like content
    cleaned_html = re.sub(r'javascript:', '', cleaned_html, flags=re.IGNORECASE)
    cleaned_html = re.sub(r'on\w+\s*=', '', cleaned_html, flags=re.IGNORECASE)
    
    return mark_safe(cleaned_html)

@register.filter
def safe_text(value):
    """
    Safely render plain text content with line breaks
    """
    if not value:
        return ''
    
    # Escape HTML and convert line breaks
    escaped = escape(value)
    with_breaks = escaped.replace('\n', '<br>')
    
    return mark_safe(with_breaks)

@register.filter
def truncate_safe_html(value, length=500):
    """
    Truncate HTML content safely while preserving basic formatting
    """
    if not value:
        return ''
    
    # First clean the HTML
    cleaned = safe_html(value)
    
    # Strip tags for length calculation
    text_only = strip_tags(cleaned)
    
    if len(text_only) <= length:
        return cleaned
    
    # Truncate and add ellipsis
    truncated_text = text_only[:length] + '...'
    
    # For truncated content, just return escaped text with breaks
    return safe_text(truncated_text)

@register.filter
def email_preview(value, length=200):
    """
    Generate a safe preview of email content
    """
    if not value:
        return ''
    
    # Strip all HTML tags for preview
    text_only = strip_tags(value)
    
    # Truncate and escape
    if len(text_only) > length:
        text_only = text_only[:length] + '...'
    
    return escape(text_only)
