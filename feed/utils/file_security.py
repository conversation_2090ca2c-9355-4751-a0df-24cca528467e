"""
File upload security utilities
"""
import os
import magic
import hashlib
import tempfile
from PIL import Image
from django.core.exceptions import ValidationError
from django.conf import settings
from werkzeug.utils import secure_filename
import logging

logger = logging.getLogger(__name__)


class FileSecurityValidator:
    """
    Comprehensive file upload security validation
    """
    
    # Maximum file sizes (in bytes)
    MAX_FILE_SIZES = {
        'image': 5 * 1024 * 1024,  # 5MB for images
        'document': 10 * 1024 * 1024,  # 10MB for documents
        'cv': 5 * 1024 * 1024,  # 5MB for CVs
        'default': 2 * 1024 * 1024,  # 2MB default
    }
    
    # Allowed file types and their MIME types
    ALLOWED_FILE_TYPES = {
        'image': {
            'extensions': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'],
            'mime_types': [
                'image/jpeg', 'image/png', 'image/gif', 
                'image/bmp', 'image/webp'
            ]
        },
        'document': {
            'extensions': ['.pdf', '.doc', '.docx', '.txt', '.rtf'],
            'mime_types': [
                'application/pdf', 'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'text/plain', 'application/rtf'
            ]
        },
        'cv': {
            'extensions': ['.pdf', '.doc', '.docx'],
            'mime_types': [
                'application/pdf', 'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            ]
        }
    }
    
    # Dangerous file signatures (magic bytes)
    DANGEROUS_SIGNATURES = [
        b'\x4d\x5a',  # PE executable
        b'\x7f\x45\x4c\x46',  # ELF executable
        b'\xca\xfe\xba\xbe',  # Java class file
        b'\xfe\xed\xfa\xce',  # Mach-O executable
        b'\x50\x4b\x03\x04',  # ZIP (could contain malware)
    ]
    
    @classmethod
    def validate_file_upload(cls, uploaded_file, file_type='default', max_size=None):
        """
        Comprehensive file upload validation
        
        Args:
            uploaded_file: Django UploadedFile object
            file_type: Type of file ('image', 'document', 'cv', 'default')
            max_size: Maximum file size override
        
        Returns:
            dict: Validation result with sanitized filename and metadata
        
        Raises:
            ValidationError: If file fails validation
        """
        if not uploaded_file:
            raise ValidationError("No file provided.")
        
        # Get file info
        original_filename = uploaded_file.name
        file_size = uploaded_file.size
        
        # Validate filename
        sanitized_filename = cls.validate_filename(original_filename)
        
        # Validate file size
        max_allowed_size = max_size or cls.MAX_FILE_SIZES.get(file_type, cls.MAX_FILE_SIZES['default'])
        if file_size > max_allowed_size:
            raise ValidationError(f"File size ({file_size} bytes) exceeds maximum allowed size ({max_allowed_size} bytes).")
        
        # Validate file extension
        file_extension = cls.get_file_extension(sanitized_filename)
        if file_type in cls.ALLOWED_FILE_TYPES:
            allowed_extensions = cls.ALLOWED_FILE_TYPES[file_type]['extensions']
            if file_extension not in allowed_extensions:
                raise ValidationError(f"File type {file_extension} not allowed for {file_type} uploads.")
        
        # Read file content for validation
        file_content = uploaded_file.read()
        uploaded_file.seek(0)  # Reset file pointer
        
        # Validate file signature
        cls.validate_file_signature(file_content, file_type)
        
        # Validate MIME type
        cls.validate_mime_type(file_content, file_type)
        
        # Additional validation for images
        if file_type == 'image':
            cls.validate_image_file(uploaded_file)
        
        # Generate secure filename
        secure_name = cls.generate_secure_filename(sanitized_filename)
        
        return {
            'original_filename': original_filename,
            'secure_filename': secure_name,
            'file_size': file_size,
            'file_extension': file_extension,
            'content_hash': hashlib.sha256(file_content).hexdigest()
        }
    
    @classmethod
    def validate_filename(cls, filename):
        """
        Validate and sanitize filename
        """
        if not filename:
            raise ValidationError("Filename cannot be empty.")
        
        # Use werkzeug's secure_filename
        sanitized = secure_filename(filename)
        
        if not sanitized:
            raise ValidationError("Invalid filename.")
        
        # Additional length check
        if len(sanitized) > 255:
            raise ValidationError("Filename too long.")
        
        # Check for dangerous patterns
        dangerous_patterns = ['..', '/', '\\', ':', '*', '?', '"', '<', '>', '|']
        for pattern in dangerous_patterns:
            if pattern in sanitized:
                raise ValidationError(f"Filename contains dangerous character: {pattern}")
        
        return sanitized
    
    @classmethod
    def get_file_extension(cls, filename):
        """
        Get file extension in lowercase
        """
        return os.path.splitext(filename)[1].lower()
    
    @classmethod
    def validate_file_signature(cls, file_content, file_type):
        """
        Validate file signature (magic bytes)
        """
        if len(file_content) < 4:
            raise ValidationError("File too small to validate.")
        
        # Check for dangerous signatures
        file_header = file_content[:8]
        for dangerous_sig in cls.DANGEROUS_SIGNATURES:
            if file_header.startswith(dangerous_sig):
                raise ValidationError("File contains dangerous signature.")
        
        # Additional checks for specific file types
        if file_type == 'image':
            # Check for common image signatures
            image_signatures = [
                b'\xff\xd8\xff',  # JPEG
                b'\x89\x50\x4e\x47',  # PNG
                b'\x47\x49\x46\x38',  # GIF
                b'\x42\x4d',  # BMP
            ]
            
            valid_signature = any(file_header.startswith(sig) for sig in image_signatures)
            if not valid_signature:
                raise ValidationError("Invalid image file signature.")
    
    @classmethod
    def validate_mime_type(cls, file_content, file_type):
        """
        Validate MIME type using python-magic
        """
        try:
            mime_type = magic.from_buffer(file_content, mime=True)
            
            if file_type in cls.ALLOWED_FILE_TYPES:
                allowed_mimes = cls.ALLOWED_FILE_TYPES[file_type]['mime_types']
                if mime_type not in allowed_mimes:
                    raise ValidationError(f"MIME type {mime_type} not allowed for {file_type} uploads.")
            
        except Exception as e:
            logger.warning(f"MIME type validation failed: {e}")
            # Don't fail completely if magic library has issues
            pass
    
    @classmethod
    def validate_image_file(cls, uploaded_file):
        """
        Additional validation for image files using PIL
        """
        try:
            # Try to open and verify the image
            with Image.open(uploaded_file) as img:
                # Verify it's a valid image
                img.verify()
                
                # Check image dimensions (prevent zip bombs)
                if img.size[0] > 10000 or img.size[1] > 10000:
                    raise ValidationError("Image dimensions too large.")
                
                # Check for suspicious metadata
                if hasattr(img, '_getexif') and img._getexif():
                    exif = img._getexif()
                    # Remove potentially dangerous EXIF data
                    dangerous_tags = [0x9286, 0x010e, 0x010f]  # UserComment, ImageDescription, Make
                    for tag in dangerous_tags:
                        if tag in exif:
                            logger.warning(f"Removing potentially dangerous EXIF tag: {tag}")
            
            # Reset file pointer
            uploaded_file.seek(0)
            
        except Exception as e:
            raise ValidationError(f"Invalid image file: {e}")
    
    @classmethod
    def generate_secure_filename(cls, original_filename):
        """
        Generate a secure filename with timestamp and hash
        """
        import time
        import uuid
        
        # Get file extension
        name, ext = os.path.splitext(original_filename)
        
        # Generate unique identifier
        timestamp = str(int(time.time()))
        unique_id = str(uuid.uuid4())[:8]
        
        # Create secure filename
        secure_name = f"{timestamp}_{unique_id}_{name[:20]}{ext}"
        
        return secure_filename(secure_name)
    
    @classmethod
    def scan_file_content(cls, file_content):
        """
        Basic content scanning for malicious patterns
        """
        # Convert to string for pattern matching
        try:
            content_str = file_content.decode('utf-8', errors='ignore').lower()
        except:
            content_str = str(file_content).lower()
        
        # Dangerous patterns in file content
        dangerous_patterns = [
            'javascript:', 'vbscript:', '<script', 'eval(', 'document.cookie',
            'window.location', 'document.write', 'innerHTML', 'outerhtml',
            'onload=', 'onerror=', 'onclick=', 'onmouseover='
        ]
        
        for pattern in dangerous_patterns:
            if pattern in content_str:
                logger.warning(f"Dangerous pattern found in file content: {pattern}")
                raise ValidationError("File contains potentially malicious content.")
        
        return True


def secure_file_upload(uploaded_file, upload_path, file_type='default', max_size=None):
    """
    Secure file upload wrapper function
    
    Args:
        uploaded_file: Django UploadedFile object
        upload_path: Path where file should be stored
        file_type: Type of file validation to apply
        max_size: Maximum file size override
    
    Returns:
        dict: Upload result with file info
    """
    try:
        # Validate the file
        validation_result = FileSecurityValidator.validate_file_upload(
            uploaded_file, file_type, max_size
        )
        
        # Create upload directory if it doesn't exist
        os.makedirs(upload_path, exist_ok=True)
        
        # Generate final file path
        secure_filename = validation_result['secure_filename']
        file_path = os.path.join(upload_path, secure_filename)
        
        # Save the file
        with open(file_path, 'wb') as destination:
            for chunk in uploaded_file.chunks():
                destination.write(chunk)
        
        # Set secure file permissions
        os.chmod(file_path, 0o644)
        
        validation_result['file_path'] = file_path
        validation_result['success'] = True
        
        return validation_result
        
    except ValidationError as e:
        logger.error(f"File upload validation failed: {e}")
        return {
            'success': False,
            'error': str(e)
        }
    except Exception as e:
        logger.error(f"File upload failed: {e}")
        return {
            'success': False,
            'error': 'File upload failed due to server error.'
        }
