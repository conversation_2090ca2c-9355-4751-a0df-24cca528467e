{% extends 'main.html' %}

{% load static %}
{% load i18n %}
{% block content %}
<!-- Include Material Icons -->
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

<div class="container">
    <div class="invitations-header">
        <h1>{% trans "Team & Invitations" %}</h1>
        <p class="invitations-subtitle">{% trans "Manage your recruitment team and invite new members" %}</p>

        <nav class="breadcrumb">
            <a href="{% url 'settings' %}">{% trans "Settings" %}</a>
            <span class="material-icons">chevron_right</span>
            <span>{% trans "Invitations" %}</span>
        </nav>
    </div>

    <!-- Team Statistics -->
    <div class="stats-container">
        <div class="stat-card">
            <div class="stat-icon">
                <span class="material-icons">people</span>
            </div>
            <div class="stat-content">
                <div class="stat-value">{{team_members|length}}</div>
                <div class="stat-label">{% trans "Team Members" %}</div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <span class="material-icons">mail</span>
            </div>
            <div class="stat-content">
                <div class="stat-value">{{ invitations|length }}</div>
                <div class="stat-label">{% trans "Invitations" %}</div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <span class="material-icons">how_to_reg</span>
            </div>
            <div class="stat-content">
                <div class="stat-value">{{ total_recruiters }}</div>
                <div class="stat-label">{% trans "Recruiters" %}</div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <span class="material-icons">admin_panel_settings</span>
            </div>
            <div class="stat-content">
                <div class="stat-value">{{ total_admins }}</div>
                <div class="stat-label">{% trans "Administrators" %}</div>
            </div>
        </div>
    </div>

    <!-- Tabs Navigation -->
    <div class="tabs-container">
        <div class="tabs">
            <button class="tab-btn active" data-tab="team-members">
                <span class="material-icons">people</span>
                {% trans "Team Members" %}
            </button>
            <button class="tab-btn" data-tab="invitations">
                <span class="material-icons">send</span>
                {% trans "Invitations" %}
            </button>
        </div>

        <div class="tab-content">
            <!-- Team Members Tab -->
            <div class="tab-pane active" id="team-members">
                <div class="tab-header">
                    <div class="search-filter">
                        <div class="search-box">
                            <span class="material-icons">search</span>
                            <input type="text" id="search-members" placeholder="{% trans 'Search team members...' %}">
                        </div>

                        <div class="filter-container">
                            <label for="role-filter">{% trans "Role:" %}</label>
                            <select id="role-filter">
                                <option value="">{% trans "All Roles" %}</option>
                                {% for role in roles %}
                                <option value="{{ role|lower }}">{% trans role %}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                </div>

                <div class="team-list-container">
                    <table class="team-table">
                        <thead>
                            <tr>
                                <th class="sortable" data-sort="name">
                                    {% trans "Name" %}
                                    <span class="material-icons sort-icon">arrow_downward</span>
                                </th>
                                <th class="sortable" data-sort="email">
                                    {% trans "Email" %}
                                    <span class="material-icons sort-icon">unfold_more</span>
                                </th>
                                <th class="sortable" data-sort="role">
                                    {% trans "Role" %}
                                    <span class="material-icons sort-icon">unfold_more</span>
                                </th>
                                <th class="sortable" data-sort="status">
                                    {% trans "Status" %}
                                    <span class="material-icons sort-icon">unfold_more</span>
                                </th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for member in team_members %}
                            <tr>
                                <td>
                                    <div class="user-info">
                                        <div class="user-avatar">{{ member.user.first_name|first }}{{ member.user.last_name|first }}</div>
                                        <div class="user-name">{{ member.user.first_name }} {{ member.user.last_name }}</div>
                                    </div>
                                </td>
                                <td>{{ member.user.email }}</td>
                                <td>
                                    <span class="role-badge {% if member.role == 'Administrator' %}admin{% elif member.role == 'Recruiter' %}recruiter{% elif member.role == 'Hiring Manager' %}hiring-manager{% elif member.role == 'Interviewer' %}interviewer{% else %}readonly{% endif %}">{{ member.role }}</span>
                                </td>
                                <td>
                                    <span class="invitation-status {{ member.status|lower }}">{{ member.status }}</span>
                                </td>
                                <td>
                                    <div class="invitation-actions">
                                    {% if member.status == 'Active' %}
                                        <button class="btn btn-danger" title="Deactivate" {% if not cur_employee.role == 'Administrator' %} disabled {% endif %} onclick="window.location.href='{% url 'change_employee_status' user_id=member.user_id status='Inactive' %}'">
                                            {% trans "Deactivate" %}
                                        </button>
                                    {% else %}
                                        <button class="btn btn-success" title="Activate" {% if not cur_employee.role == 'Administrator' %} disabled {% endif %} onclick="window.location.href='{% url 'change_employee_status' user_id=member.user_id status='Active' %}'">
                                            {% trans "Activate" %}
                                        </button>
                                    {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>

                    <div class="pagination">
                        <span class="pagination-info">Showing {{ team_members.start_index }} - {{ team_members.end_index }} of {{ team_members.paginator.count }} Members </span>
                        <div class="pagination-controls">
                            {% if team_members.has_previous %}
                            <a href="?inv_page={{ team_members.previous_page_number }}" class="pagination-btn">
                                <span class="material-icons">chevron_left</span>
                            </a>
                            {% else %}
                            <button class="pagination-btn" disabled>
                                <span class="material-icons">chevron_left</span>
                            </button>
                            {% endif %}

                            {% for i in team_members.paginator.page_range %}
                                {% if i == team_members.number %}
                                <button class="pagination-btn page-number active">{{ i }}</button>
                                {% else %}
                                <a href="?inv_page={{ i }}" class="pagination-btn page-number">{{ i }}</a>
                                {% endif %}
                            {% endfor %}

                            {% if team_members.has_next %}
                            <a href="?inv_page={{ team_members.next_page_number }}" class="pagination-btn">
                                <span class="material-icons">chevron_right</span>
                            </a>
                            {% else %}
                            <button class="pagination-btn" disabled>
                                <span class="material-icons">chevron_right</span>
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>









            <!-- Invitations Tab -->
            <div class="tab-pane" id="invitations">
                <div class="tab-header">
                    <div class="search-filter">
                        <div class="search-box">
                            <span class="material-icons">search</span>
                            <input type="text" id="search-invitations" placeholder="{% trans 'Search invitations...' %}">
                        </div>

                        <div class="filter-container">
                            <label for="invitation-status-filter">{% trans "Status:" %}</label>
                            <select id="invitation-status-filter">
                                <option value="">{% trans "All Statuses" %}</option>
                                {% for status in invitation_statuses %}
                                <option value="{{ status|lower }}">{% trans status %}</option>
                                {% endfor %}
                            </select>

                            <label for="invitation-role-filter">{% trans "Role:" %}</label>
                            <select id="invitation-role-filter">
                                <option value="">{% trans "All Roles" %}</option>
                                {% for role in roles %}
                                <option value="{{ role|lower }}">{% trans role %}</option>
                                {% endfor %}
                            </select>
                        </div>


                        <div class="action-buttons">
                            <button class="invite-btn" id="invite-new-member">
                                <span class="material-icons">person_add</span>
                                {% trans "Invite New Member" %}
                            </button>
                        </div>

                    </div>

                </div>

                <div class="invitations-list-container">
                    <table class="invitations-table">
                        <thead>
                            <tr>
                                <th class="sortable" data-sort="name">
                                    {% trans "Name" %}
                                    <span class="material-icons sort-icon">arrow_downward</span>
                                </th>
                                <th class="sortable" data-sort="email">
                                    {% trans "Email" %}
                                    <span class="material-icons sort-icon">unfold_more</span>
                                </th>
                                <th class="sortable" data-sort="role">
                                    {% trans "Role" %}
                                    <span class="material-icons sort-icon">unfold_more</span>
                                </th>
                                <th class="sortable" data-sort="sent-date">
                                    {% trans "Sent Date" %}
                                    <span class="material-icons sort-icon">unfold_more</span>
                                </th>
                                <th class="sortable" data-sort="expiry-date">
                                    {% trans "Expiry Date" %}
                                    <span class="material-icons sort-icon">unfold_more</span>
                                </th>
                                <th class="sortable" data-sort="status">
                                    {% trans "Status" %}
                                    <span class="material-icons sort-icon">unfold_more</span>
                                </th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>

                        <tbody>
                            {% for invitation in invitations %}
                            <tr>
                                <td>
                                    <div class="user-info">
                                        <div class="user-avatar">{{ invitation.first_name|first }}{{ invitation.last_name|first }}</div>
                                        <div class="user-name">{{ invitation.first_name }} {{ invitation.last_name }}</div>
                                    </div>
                                </td>
                                <td>{{ invitation.email }}</td>
                                <td>
                                    <span class="role-badge {% if invitation.role == 'Administrator' %}admin{% elif invitation.role == 'Recruiter' %}recruiter{% elif invitation.role == 'Hiring Manager' %}hiring-manager{% elif invitation.role == 'Interviewer' %}interviewer{% else %}readonly{% endif %}">{{ invitation.role }}</span>
                                </td>
                                <td>{{ invitation.sent_date|date:"M d, Y" }}</td>
                                <td>{{ invitation.expiry_date|date:"M d, Y" }}</td>
                                <td>
                                    <span class="invitation-status {{ invitation.invitation_status|lower }}">{{ invitation.invitation_status }}</span>
                                </td>
                                <td>
                                    <div class="invitation-actions">
                                        {% if invitation.invitation_status == 'Pending' %}
                                        <a class="action-btn" href="{% url 'change_invitation_status' invitation_id=invitation.id status='Canceled' %}" title="Cancel">
                                            <span class="material-icons">cancel</span>
                                        </a>
                                        {% elif invitation.invitation_status == 'Canceled' %}
                                        <a class="action-btn" href="{% url 'change_invitation_status' invitation_id=invitation.id status='Deleted' %}" title="Delete">
                                            <span class="material-icons">delete</span>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="7" style="text-align: center; padding: 30px;">{% trans "No invitations found" %}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>

                    <div class="pagination">
                        <span class="pagination-info">Showing {{ invitations.start_index }} - {{ invitations.end_index }} of {{ invitations.paginator.count }} invitations</span>
                        <div class="pagination-controls">
                            {% if invitations.has_previous %}
                            <a href="?inv_page={{ invitations.previous_page_number }}" class="pagination-btn">
                                <span class="material-icons">chevron_left</span>
                            </a>
                            {% else %}
                            <button class="pagination-btn" disabled>
                                <span class="material-icons">chevron_left</span>
                            </button>
                            {% endif %}

                            {% for i in invitations.paginator.page_range %}
                                {% if i == invitations.number %}
                                <button class="pagination-btn page-number active">{{ i }}</button>
                                {% else %}
                                <a href="?inv_page={{ i }}" class="pagination-btn page-number">{{ i }}</a>
                                {% endif %}
                            {% endfor %}

                            {% if invitations.has_next %}
                            <a href="?inv_page={{ invitations.next_page_number }}" class="pagination-btn">
                                <span class="material-icons">chevron_right</span>
                            </a>
                            {% else %}
                            <button class="pagination-btn" disabled>
                                <span class="material-icons">chevron_right</span>
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Invitation Modal -->
<div class="modal" id="invitation-modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>{% trans "Invite New Team Member" %}</h2>
            <span class="close-modal">&times;</span>
        </div>
        <div class="modal-body">
            <form id="invitation-form" method="post">
                {% csrf_token %}
                <div class="form-section">
                    <h3>{% trans "Recipient Information" %}</h3>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="first_name">{% trans "First Name" %}</label>
                            <input type="text" id="first_name" name="first_name" placeholder="{% trans 'Enter first name' %}" required>
                        </div>

                        <div class="form-group">
                            <label for="last_name">{% trans "Last Name" %}</label>
                            <input type="text" id="last_name" name="last_name" placeholder="{% trans 'Enter last name' %}" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="email">{% trans "Email Address" %}</label>
                        <input type="email" id="email" name="email" placeholder="{% trans 'Enter email address' %}" required>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="role">{% trans "Role" %}</label>
                            <select id="role" name="role" required>
                                <option value="">{% trans "Select a role" %}</option>
                                <option value="Administrator">{% trans "Administrator" %}</option>
                                <option value="Recruiter">{% trans "Recruiter" %}</option>
                                <option value="Hiring Manager">{% trans "Hiring Manager" %}</option>
                                <option value="Interviewer">{% trans "Interviewer" %}</option>
                                <option value="Read Only">{% trans "Read Only" %}</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-section permissions-section">
                    <h3>{% trans "Permissions" %}</h3>
                    <p class="permissions-note">{% trans "Permissions are determined by the selected role. You can customize them after the user has accepted the invitation." %}</p>

                    <div class="permissions-preview">
                        <div class="permission-group">
                            <h4>{% trans "Role Descriptions:" %}</h4>
                            <ul class="permissions-list">
                                <li>{% trans "Administrator: Full access to all system features and settings." %}</li>
                                <li>{% trans "Recruiter: Manage job postings, candidates, and interviews." %}</li>
                                <li>{% trans "Hiring Manager: Review candidates and make hiring decisions." %}</li>
                                <li>{% trans "Interviewer: Conduct interviews and provide feedback." %}</li>
                                <li>{% trans "Read Only: View-only access to recruitment data." %}</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="cancel-btn" id="cancel-invitation-form">{% trans "Cancel" %}</button>
                    <button type="submit" class="confirm-btn" id="send-invitation">{% trans "Send Invitation" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- TODO: Role Change Modal -->
<div class="modal" id="role-change-modal">
    <div class="modal-content modal-sm">
        <div class="modal-header">
            <h2>{% trans "Change Role" %}</h2>
            <span class="close-modal">&times;</span>
        </div>
        <div class="modal-body">
            <form id="role-change-form">
                <div class="form-group">
                    <label for="member-name">{% trans "Team Member" %}</label>
                    <input type="text" id="member-name" value="Jane Smith" disabled>
                </div>

                <div class="form-group">
                    <label for="current-role">{% trans "Current Role" %}</label>
                    <input type="text" id="current-role" value="{% trans "Recruiter" %}" disabled>
                </div>

                <div class="form-group">
                    <label for="new-role">{% trans "New Role*" %}</label>
                    <select id="new-role" required>
                        <option value="">{% trans "Select a role" %}</option>
                        <option value="admin">{% trans "Administrator" %}</option>
                        <option value="recruiter">{% trans "Recruiter" %}</option>
                        <option value="hiring-manager">{% trans "Hiring Manager" %}</option>
                        <option value="interviewer">{% trans "Interviewer" %}</option>
                        <option value="readonly">{% trans "Read Only" %}</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="change-reason">{% trans "Reason for Change (Optional)" %}</label>
                    <textarea id="change-reason" rows="3" placeholder="{% trans 'Provide a reason for this role change' %}"></textarea>
                </div>

                <div class="permission-alert">
                    <span class="material-icons">info</span>
                    <p>{% trans "Changing roles will update the user's permissions. They will be notified of this change." %}</p>
                </div>

                <div class="modal-footer">
                    <button type="button" class="cancel-btn" id="cancel-role-change">{% trans "Cancel" %}</button>
                    <button type="submit" class="confirm-btn" id="confirm-role-change">{% trans "Change Role" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    :root {
        --primary: #4a6cf7;
        --primary-hover: #3859e9;
        --secondary: #f5f8ff;
        --success: #28a745;
        --warning: #ffc107;
        --danger: #dc3545;
        --light-gray: #f1f2f6;
        --text-color: #333;
        --border-color: #ddd;
        --hover-bg: #f9fafb;
        --shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    body {
        background-color: #f9fafc;
        color: var(--text-color);
        line-height: 1.6;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 40px 20px;
    }

    /* Header */
    .invitations-header {
        margin-bottom: 30px;
    }

    .invitations-header h1 {
        font-size: 28px;
        color: #252b42;
        margin-bottom: 10px;
    }

    .invitations-subtitle {
        font-size: 16px;
        color: #666;
        margin-bottom: 20px;
    }

    /* Breadcrumb */
    .breadcrumb {
        display: flex;
        align-items: center;
        font-size: 14px;
    }

    .breadcrumb a {
        color: var(--primary);
        text-decoration: none;
    }

    .breadcrumb a:hover {
        text-decoration: underline;
    }

    .breadcrumb .material-icons {
        font-size: 16px;
        margin: 0 8px;
        color: #999;
    }

    /* Stats Container */
    .stats-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background-color: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: var(--shadow);
        display: flex;
        align-items: center;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: var(--secondary);
        color: var(--primary);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
    }

    .stat-icon .material-icons {
        font-size: 24px;
    }

    .stat-content {
        flex: 1;
    }

    .stat-value {
        font-size: 24px;
        font-weight: 700;
        color: #252b42;
    }

    .stat-label {
        font-size: 14px;
        color: #666;
    }

    /* Tabs Container */
    .tabs-container {
        background-color: white;
        border-radius: 8px;
        box-shadow: var(--shadow);
        overflow: hidden;
        margin-bottom: 30px;
    }

    /* Tabs Navigation */
    .tabs {
        display: flex;
        overflow-x: auto;
        border-bottom: 1px solid var(--border-color);
        background-color: #f9fafb;
    }

    .tab-btn {
        padding: 15px 20px;
        background: none;
        border: none;
        cursor: pointer;
        font-size: 15px;
        font-weight: 500;
        color: #666;
        border-bottom: 3px solid transparent;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s;
        white-space: nowrap;
    }

    .tab-btn:hover {
        background-color: #f0f4ff;
        color: var(--primary);
    }

    .tab-btn.active {
        color: var(--primary);
        border-bottom-color: var(--primary);
        background-color: white;
    }

    .tab-btn .material-icons {
        font-size: 20px;
    }

    .badge {
        background-color: var(--primary);
        color: white;
        font-size: 12px;
        border-radius: 12px;
        padding: 2px 8px;
        margin-left: 5px;
    }

    /* Tab Content */
    .tab-content {
        min-height: 400px;
    }

    .tab-pane {
        display: none;
    }

    .tab-pane.active {
        display: block;
        animation: fadeIn 0.3s;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    /* Tab Header */
    .tab-header {
        padding: 20px;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: center;
        gap: 15px;
    }

    /* Search and Filter */
    .search-filter {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 15px;
        flex: 1;
    }

    .search-box {
        position: relative;
        flex: 1;
        max-width: 300px;
    }

    .search-box .material-icons {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #999;
        font-size: 18px;
    }

    .search-box input {
        width: 100%;
        padding: 10px 10px 10px 35px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        font-size: 14px;
    }

    .search-box input:focus {
        outline: none;
        border-color: var(--primary);
    }

    .filter-container {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 10px;
    }

    .filter-container label {
        font-size: 14px;
        color: #666;
    }

    .filter-container select {
        padding: 8px 10px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        font-size: 14px;
        min-width: 120px;
        color: var(--text-color);
    }

    .filter-container select:focus {
        outline: none;
        border-color: var(--primary);
    }

    /* Action Buttons */
    .action-buttons {
        display: flex;
        gap: 10px;
    }

    .invite-btn {
        background-color: var(--primary);
        color: white;
        border: none;
        padding: 10px 16px;
        border-radius: 4px;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 6px;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .invite-btn:hover {
        background-color: var(--primary-hover);
    }

    /* Team Table */
    .team-list-container,
    .invitations-list-container {
        padding: 20px;
    }

    .team-table,
    .invitations-table {
        width: 100%;
        border-collapse: collapse;
    }

    .team-table th,
    .team-table td,
    .invitations-table th,
    .invitations-table td {
        padding: 12px 15px;
        text-align: left;
        border-bottom: 1px solid var(--border-color);
    }

    .team-table th,
    .invitations-table th {
        font-weight: 600;
        color: #666;
        background-color: var(--hover-bg);
        position: relative;
    }

    .sortable {
        cursor: pointer;
    }

    .sortable:hover {
        background-color: #f0f0f0;
    }

    .sort-icon {
        font-size: 16px !important;
        vertical-align: middle;
        margin-left: 5px;
    }

    .user-info {
        display: flex;
        align-items: center;
    }

    .user-avatar {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        background-color: var(--primary);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        margin-right: 10px;
    }

    .user-name {
        font-weight: 500;
    }

    /* Role Badges */
    .role-badge {
        display: inline-block;
        padding: 3px 10px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }

    .role-badge.admin {
        background-color: #d0e1fd;
        color: #2962ff;
    }

    .role-badge.recruiter {
        background-color: #d1f7c4;
        color: #2e7d32;
    }

    .role-badge.hiring-manager {
        background-color: #ffecb3;
        color: #ff8f00;
    }

    .role-badge.interviewer {
        background-color: #e1bee7;
        color: #7b1fa2;
    }

    .role-badge.readonly {
        background-color: #e0e0e0;
        color: #616161;
    }

    /* Status Indicators */
    .status-indicator {
        display: inline-flex;
        align-items: center;
        padding: 3px 10px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }

    .status-indicator.active {
        background-color: #d1f7c4;
        color: #2e7d32;
    }

    .status-indicator.inactive {
        background-color: #ffcdd2;
        color: #c62828;
    }

    /* Action Menu */
    .action-menu {
        position: relative;
    }

    .action-menu-btn {
        background: none;
        border: none;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        cursor: pointer;
        color: #666;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .action-menu-btn:hover {
        background-color: var(--hover-bg);
    }

    .action-menu-dropdown {
        position: absolute;
        right: 0;
        top: 35px;
        background-color: white;
        border-radius: 4px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        min-width: 150px;
        z-index: 10;
        display: none;
    }

    .action-menu:hover .action-menu-dropdown {
        display: block;
    }

    .action-item {
        display: flex;
        align-items: center;
        padding: 8px 15px;
        color: var(--text-color);
        text-decoration: none;
        font-size: 14px;
        gap: 8px;
    }

    .action-item:hover {
        background-color: var(--hover-bg);
    }

    .action-item .material-icons {
        font-size: 16px;
    }

    .action-item.edit-member {
        color: var(--primary);
    }

    .action-item.change-role {
        color: var(--warning);
    }

    .action-item.deactivate-member,
    .action-item.delete-member {
        color: var(--danger);
    }

    .action-item.activate-member {
        color: var(--success);
    }

    /* Invitation Statuses */
    .invitation-status {
        display: inline-flex;
        align-items: center;
        padding: 3px 10px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }

    .invitation-status.pending {
        background-color: #fff8e1;
        color: #ff8f00;
    }

    .invitation-status.accepted {
        background-color: #d1f7c4;
        color: #2e7d32;
    }

    .invitation-status.expired {
        background-color: #e0e0e0;
        color: #616161;
    }

    .invitation-status.canceled {
        background-color: #ffcdd2;
        color: #c62828;
    }

    /* Invitation Actions */
    .invitation-actions {
        display: flex;
        gap: 8px;
    }

    .action-btn {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: none;
        border: none;
        cursor: pointer;
        color: #666;
        transition: all 0.2s;
    }

    .action-btn:hover {
        background-color: var(--hover-bg);
    }

    .action-btn .material-icons {
        font-size: 18px;
    }

    .resend-invitation:hover {
        color: var(--primary);
    }

    .cancel-invitation:hover,
    .delete-invitation:hover {
        color: var(--danger);
    }

    .view-member:hover {
        color: var(--success);
    }

    /* Pagination */
    .pagination {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid var(--border-color);
    }

    .pagination-info {
        font-size: 14px;
        color: #666;
    }

    .pagination-controls {
        display: flex;
        gap: 5px;
    }

    .pagination-btn {
        width: 32px;
        height: 32px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: white;
        border: 1px solid var(--border-color);
        cursor: pointer;
        color: var(--text-color);
    }

    .pagination-btn.active {
        background-color: var(--primary);
        color: white;
        border-color: var(--primary);
    }

    .pagination-btn:disabled {
        cursor: not-allowed;
        opacity: 0.5;
    }

    .pagination-btn:not(:disabled):hover {
        background-color: var(--hover-bg);
    }

    .pagination-btn.active:hover {
        background-color: var(--primary-hover);
    }

    /* Organization Chart */
    .org-chart-container {
        background-color: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: var(--shadow);
        margin-bottom: 30px;
    }

    .org-chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .org-chart-header h2 {
        font-size: 20px;
        color: #252b42;
        margin: 0;
    }

    .expand-btn {
        background-color: var(--secondary);
        color: var(--primary);
        border: none;
        padding: 8px 15px;
        border-radius: 4px;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 5px;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .expand-btn:hover {
        background-color: #e6f0ff;
    }

    .org-chart {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;
        overflow-x: auto;
    }

    .org-level {
        display: flex;
        justify-content: center;
        gap: 30px;
        margin-bottom: 20px;
        width: 100%;
    }

    .org-node {
        display: flex;
        align-items: center;
        padding: 10px 15px;
        background-color: white;
        border-radius: 6px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        position: relative;
    }

    .org-node::before {
        content: '';
        position: absolute;
        top: -20px;
        left: 50%;
        height: 20px;
        width: 2px;
        background-color: #ccc;
    }

    .org-level:first-child .org-node::before {
        display: none;
    }

    .org-node.admin {
        border-left: 4px solid #2962ff;
    }

    .org-node.recruiter {
        border-left: 4px solid #2e7d32;
    }

    .org-node.hiring-manager {
        border-left: 4px solid #ff8f00;
    }

    .org-node.interviewer {
        border-left: 4px solid #7b1fa2;
    }

    .org-avatar {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        background-color: var(--primary);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        margin-right: 10px;
    }

    .org-details {
        display: flex;
        flex-direction: column;
    }

    .org-name {
        font-weight: 500;
        margin-bottom: 2px;
    }

    .org-role {
        font-size: 12px;
        color: #666;
    }

    .org-status {
        font-size: 11px;
        font-weight: 500;
        margin-top: 4px;
    }

    .org-status.pending {
        color: #ff8f00;
    }

    .org-connector {
        width: 2px;
        background-color: #ccc;
        margin: 0 auto;
    }

    /* Modal Styles */
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        overflow: auto;
    }

    .modal-content {
        background-color: white;
        margin: 5% auto;
        max-width: 600px;
        width: 90%;
        border-radius: 8px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
        animation: modalFadeIn 0.3s;
    }

    .modal-content.modal-sm {
        max-width: 450px;
    }

    @keyframes modalFadeIn {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .modal-header {
        padding: 20px;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-header h2 {
        margin: 0;
        font-size: 20px;
    }

    .close-modal {
        font-size: 24px;
        cursor: pointer;
        color: #999;
    }

    .close-modal:hover {
        color: #333;
    }

    .modal-body {
        padding: 20px;
        max-height: 70vh;
        overflow-y: auto;
    }

    /* Form Elements */
    .form-section {
        margin-bottom: 25px;
    }

    .form-section h3 {
        font-size: 18px;
        color: #252b42;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid var(--border-color);
    }

    .form-row {
        display: flex;
        gap: 15px;
        margin-bottom: 15px;
    }

    @media (max-width: 600px) {
        .form-row {
            flex-direction: column;
            gap: 0;
        }
    }

    .form-group {
        margin-bottom: 15px;
        flex: 1;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        font-size: 14px;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        font-size: 14px;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: var(--primary);
    }

    .form-group input:disabled,
    .form-group select:disabled,
    .form-group textarea:disabled {
        background-color: var(--light-gray);
        cursor: not-allowed;
    }

    /* Permissions Section */
    .permissions-section h3 {
        margin-bottom: 10px;
    }

    .permissions-note {
        color: #666;
        font-size: 14px;
        margin-bottom: 15px;
    }

    .permissions-preview {
        background-color: var(--light-gray);
        border-radius: 6px;
        padding: 15px;
    }

    .permission-group h4 {
        font-size: 15px;
        color: #252b42;
        margin-bottom: 10px;
    }

    .permissions-list {
        list-style: none;
    }

    .permissions-list li {
        padding: 5px 0;
        display: flex;
        align-items: center;
        font-size: 14px;
    }

    .check-icon {
        color: var(--success);
        margin-right: 8px;
        font-size: 18px !important;
    }

    .cancel-icon {
        color: var(--danger);
        margin-right: 8px;
        font-size: 18px !important;
    }

    /* Permission Alert */
    .permission-alert {
        display: flex;
        align-items: flex-start;
        gap: 10px;
        background-color: var(--secondary);
        border-radius: 6px;
        padding: 12px;
        margin-top: 15px;
    }

    .permission-alert .material-icons {
        color: var(--primary);
        font-size: 20px;
    }

    .permission-alert p {
        flex: 1;
        font-size: 14px;
        color: #555;
    }

    /* Bulk Upload */
    .bulk-upload-container {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
    }

    @media (max-width: 768px) {
        .bulk-upload-container {
            flex-direction: column;
        }
    }

    .upload-instructions {
        flex: 1;
    }

    .upload-instructions h3 {
        font-size: 18px;
        color: #252b42;
        margin-bottom: 10px;
    }

    .upload-instructions p {
        color: #666;
        font-size: 14px;
        margin-bottom: 10px;
    }

    .upload-instructions ul {
        margin-left: 20px;
        margin-bottom: 15px;
    }

    .upload-instructions li {
        font-size: 14px;
        margin-bottom: 5px;
    }

    .template-download {
        margin-top: 15px;
    }

    .download-link {
        display: inline-flex;
        align-items: center;
        color: var(--primary);
        text-decoration: none;
        font-size: 14px;
        gap: 5px;
    }

    .download-link:hover {
        text-decoration: underline;
    }

    .upload-area {
        flex: 1;
    }

    .drop-zone {
        border: 2px dashed var(--border-color);
        border-radius: 6px;
        padding: 30px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;
    }

    .drop-zone:hover {
        border-color: var(--primary);
        background-color: var(--secondary);
    }

    .upload-icon {
        font-size: 48px !important;
        color: #999;
        margin-bottom: 10px;
    }

    .drop-zone p {
        color: #666;
        font-size: 14px;
    }

    .upload-preview {
        margin-top: 15px;
        background-color: var(--light-gray);
        border-radius: 6px;
        padding: 15px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .file-info {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .file-info .material-icons {
        color: var(--primary);
    }

    .file-details {
        display: flex;
        flex-direction: column;
    }

    .file-name {
        font-weight: 500;
        margin-bottom: 2px;
    }

    .file-size {
        font-size: 12px;
        color: #666;
    }

    .remove-file-btn {
        background: none;
        border: none;
        color: #999;
        cursor: pointer;
    }

    .remove-file-btn:hover {
        color: var(--danger);
    }

    .bulk-options {
        margin-top: 20px;
    }

    /* Modal Footer */
    .modal-footer {
        padding-top: 20px;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }

    .cancel-btn, .confirm-btn {
        padding: 10px 16px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
    }

    .cancel-btn {
        background-color: white;
        color: #666;
        border: 1px solid var(--border-color);
    }

    .cancel-btn:hover {
        background-color: #f5f5f5;
    }

    .confirm-btn {
        background-color: var(--primary);
        color: white;
        border: none;
    }

    .confirm-btn:hover {
        background-color: var(--primary-hover);
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .tab-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .search-filter {
            flex-direction: column;
            align-items: flex-start;
            width: 100%;
        }

        .search-box {
            max-width: none;
            width: 100%;
        }

        .filter-container {
            width: 100%;
        }

        .team-table,
        .invitations-table {
            display: block;
            overflow-x: auto;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Tab switching functionality
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabPanes = document.querySelectorAll('.tab-pane');

        // Initially hide all tab panes except the first one
        tabPanes.forEach((pane, index) => {
            if (index !== 0) {
                pane.style.display = 'none';
            } else {
                pane.style.display = 'block';
            }
        });

        // Add click event listeners to all tab buttons
        tabBtns.forEach((btn) => {
            btn.addEventListener('click', function() {
                // Remove active class from all buttons
                tabBtns.forEach(b => b.classList.remove('active'));

                // Add active class to clicked button
                this.classList.add('active');

                // Get target tab ID
                const targetId = this.getAttribute('data-tab');

                // Hide all tab panes
                tabPanes.forEach(pane => {
                    pane.style.display = 'none';
                });

                // Show the target tab pane
                document.getElementById(targetId).style.display = 'block';
            });
        });

        // Action menu toggles (more vert buttons)
        const actionMenuBtns = document.querySelectorAll('.action-menu-btn');

        actionMenuBtns.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.stopPropagation();
                const dropdown = this.nextElementSibling;
                dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
            });
        });

        // Close all action menus when clicking outside
        document.addEventListener('click', function() {
            document.querySelectorAll('.action-menu-dropdown').forEach(dropdown => {
                dropdown.style.display = 'none';
            });
        });

        // Toggle sorting for table headers
        const sortableHeaders = document.querySelectorAll('.sortable');

        sortableHeaders.forEach(header => {
            header.addEventListener('click', function() {
                const sortIcon = this.querySelector('.sort-icon');

                // Reset all other sort icons
                document.querySelectorAll('.sort-icon').forEach(icon => {
                    if (icon !== sortIcon) {
                        icon.textContent = 'unfold_more';
                    }
                });

                // Toggle sort direction
                if (sortIcon.textContent === 'unfold_more') {
                    sortIcon.textContent = 'arrow_downward';
                } else if (sortIcon.textContent === 'arrow_downward') {
                    sortIcon.textContent = 'arrow_upward';
                } else {
                    sortIcon.textContent = 'arrow_downward';
                }

                // Sort the table (simplified example)
                sortTable(this);
            });
        });

        // Function to sort table
        function sortTable(headerElement) {
            const table = headerElement.closest('table');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            const columnIndex = Array.from(headerElement.parentNode.children).indexOf(headerElement);
            const direction = headerElement.querySelector('.sort-icon').textContent === 'arrow_downward' ? 'asc' : 'desc';

            // Sort the rows based on the content of the column
            rows.sort((a, b) => {
                const aValue = a.children[columnIndex].textContent.trim();
                const bValue = b.children[columnIndex].textContent.trim();

                if (direction === 'asc') {
                    return aValue.localeCompare(bValue);
                } else {
                    return bValue.localeCompare(aValue);
                }
            });

            // Remove existing rows
            rows.forEach(row => row.remove());

            // Add sorted rows
            rows.forEach(row => tbody.appendChild(row));
        }

        // Invitation Modal
        const inviteButtons = document.querySelectorAll('#invite-new-member');
        const invitationModal = document.getElementById('invitation-modal');
        const closeModalBtns = document.querySelectorAll('.close-modal');
        const cancelInvitationBtn = document.getElementById('cancel-invitation-form');

        inviteButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                invitationModal.style.display = 'block';
            });
        });

        function closeModal(modal) {
            modal.style.display = 'none';
        }

        closeModalBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                closeModal(this.closest('.modal'));
            });
        });

        if (cancelInvitationBtn) {
            cancelInvitationBtn.addEventListener('click', function() {
                closeModal(invitationModal);
            });
        }

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target.classList.contains('modal')) {
                closeModal(event.target);
            }
        });

        // Role change modal
        const changeRoleButtons = document.querySelectorAll('.change-role');
        const roleChangeModal = document.getElementById('role-change-modal');
        const cancelRoleChangeBtn = document.getElementById('cancel-role-change');

        changeRoleButtons.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // In a real implementation, you would fetch the member data here
                roleChangeModal.style.display = 'block';
            });
        });

        if (cancelRoleChangeBtn) {
            cancelRoleChangeBtn.addEventListener('click', function() {
                closeModal(roleChangeModal);
            });
        }

        // Search functionality for tables
        const searchMembers = document.getElementById('search-members');
        const searchInvitations = document.getElementById('search-invitations');

        if (searchMembers) {
            searchMembers.addEventListener('input', function() {
                filterTable('.team-table tbody tr', this.value.toLowerCase());
            });
        }

        if (searchInvitations) {
            searchInvitations.addEventListener('input', function() {
                filterTable('.invitations-table tbody tr', this.value.toLowerCase());
            });
        }

        function filterTable(selector, query) {
            const rows = document.querySelectorAll(selector);

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(query) ? '' : 'none';
            });
        }

        // Team Members Tab Filters
        const roleFilter = document.getElementById('role-filter');
        const statusFilter = document.getElementById('status-filter');

        // Invitations Tab Filters
        const invitationStatusFilter = document.getElementById('invitation-status-filter');
        const invitationRoleFilter = document.getElementById('invitation-role-filter');

        // Apply filters when any filter changes in Team Members tab
        if (roleFilter) {
            roleFilter.addEventListener('change', applyTeamFilters);
        }

        if (statusFilter) {
            statusFilter.addEventListener('change', applyTeamFilters);
        }

        // Apply filters when any filter changes in Invitations tab
        if (invitationStatusFilter) {
            invitationStatusFilter.addEventListener('change', applyInvitationFilters);
        }

        if (invitationRoleFilter) {
            invitationRoleFilter.addEventListener('change', applyInvitationFilters);
        }

        // Function to apply filters to Team Members tab
        function applyTeamFilters() {
            const roleValue = roleFilter ? roleFilter.value.toLowerCase() : '';
            const statusValue = statusFilter ? statusFilter.value.toLowerCase() : '';

            console.log("Applying filters:", roleValue, statusValue);

            const rows = document.querySelectorAll('.team-table tbody tr');

            rows.forEach(row => {
                let showRow = true;

                // Check role filter
                if (roleValue && showRow) {
                    const roleCell = row.querySelector('td:nth-child(3)');
                    const roleBadge = roleCell.querySelector('.role-badge');
                    const roleText = roleCell.textContent.trim().toLowerCase();

                    // Check both the displayed text and class
                    const roleMatch = roleText.includes(roleValue) ||
                                     (roleBadge.classList.contains(roleValue));

                    if (!roleMatch) {
                        showRow = false;
                    }
                }


                // Check status filter
                if (statusValue && showRow) {
                    const statusCell = row.querySelector('td:nth-child(5)');
                    const statusIndicator = statusCell.querySelector('.status-indicator');

                    // Check if the indicator has the class matching the status value
                    if (!statusIndicator.classList.contains(statusValue)) {
                        showRow = false;
                    }
                }

                // Show or hide the row
                row.style.display = showRow ? '' : 'none';
            });
        }

        // Function to apply filters to Invitations tab
        function applyInvitationFilters() {
            const statusValue = invitationStatusFilter ? invitationStatusFilter.value.toLowerCase() : '';
            const roleValue = invitationRoleFilter ? invitationRoleFilter.value.toLowerCase() : '';

            console.log("Applying invitation filters:", statusValue, roleValue);

            const rows = document.querySelectorAll('.invitations-table tbody tr');

            rows.forEach(row => {
                let showRow = true;

                // Check status filter
                if (statusValue && showRow) {
                    const statusCell = row.querySelector('td:nth-child(6)');
                    const statusIndicator = statusCell.querySelector('.invitation-status');

                    // Check if the indicator has the class matching the status value
                    if (!statusIndicator.classList.contains(statusValue)) {
                        showRow = false;
                    }
                }

                // Check role filter
                if (roleValue && showRow) {
                    const roleCell = row.querySelector('td:nth-child(3)');
                    const roleBadge = roleCell.querySelector('.role-badge');
                    const roleText = roleCell.textContent.trim().toLowerCase();

                    // Check both the displayed text and class
                    const roleMatch = roleText.includes(roleValue) ||
                                     (roleBadge.classList.contains(roleValue));

                    if (!roleMatch) {
                        showRow = false;
                    }
                }

                // Show or hide the row
                row.style.display = showRow ? '' : 'none';
            });
        }

        const paginationBtns = document.querySelectorAll('.pagination-btn.page-number');

        paginationBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                // Find the container
                const paginationContainer = this.closest('.pagination-controls');

                // Remove active class from all page number buttons
                paginationContainer.querySelectorAll('.page-number').forEach(b => {
                    b.classList.remove('active');
                });

                // Add active class to clicked button
                this.classList.add('active');

                // Get the page number
                const pageNum = parseInt(this.textContent);

                // Update prev/next buttons
                const prevBtn = paginationContainer.querySelector('.pagination-btn:first-child');
                const nextBtn = paginationContainer.querySelector('.pagination-btn:last-child');

                if (prevBtn) {
                    prevBtn.disabled = (pageNum === 1);
                }

                if (nextBtn) {
                    const totalPages = paginationContainer.querySelectorAll('.page-number').length;
                    nextBtn.disabled = (pageNum === totalPages);
                }

                // In a real application, you would fetch data for the requested page
                // For demo purposes, we'll simulate paging by updating the info text

                // Update the pagination info text
                const paginationInfo = this.closest('.pagination').querySelector('.pagination-info');
                if (paginationInfo) {
                    if (paginationInfo.textContent.includes('members')) {
                        paginationInfo.textContent = `Showing ${(pageNum-1)*5 + 1} - ${pageNum*5} of 12 members`;
                    } else {
                        paginationInfo.textContent = `Showing ${(pageNum-1)*6 + 1} - ${pageNum*6} of 10 invitations`;
                    }
                }
            });
        });

        // Previous and Next pagination buttons
        const prevButtons = document.querySelectorAll('.pagination-controls .pagination-btn:first-child');
        const nextButtons = document.querySelectorAll('.pagination-controls .pagination-btn:last-child');

        prevButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                if (!this.disabled) {
                    const paginationContainer = this.closest('.pagination-controls');
                    const activeBtn = paginationContainer.querySelector('.page-number.active');
                    const prevBtn = activeBtn.previousElementSibling;

                    if (prevBtn && prevBtn.classList.contains('page-number')) {
                        prevBtn.click();
                    }
                }
            });
        });

        nextButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                if (!this.disabled) {
                    const paginationContainer = this.closest('.pagination-controls');
                    const activeBtn = paginationContainer.querySelector('.page-number.active');
                    const nextBtn = activeBtn.nextElementSibling;

                    if (nextBtn && nextBtn.classList.contains('page-number')) {
                        nextBtn.click();
                    }
                }
            });
        });

        // Invitation actions
        const resendButtons = document.querySelectorAll('.resend-invitation');
        const cancelButtons = document.querySelectorAll('.cancel-invitation');
        const deleteButtons = document.querySelectorAll('.delete-invitation');

        resendButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const row = this.closest('tr');

                // Simulate loading state
                this.disabled = true;

                // In a real implementation, you would call an API here
                setTimeout(() => {
                    alert(`Invitation resent to ${row.querySelector('.user-name').textContent}`);
                    this.disabled = false;
                }, 1000);
            });
        });

        cancelButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const row = this.closest('tr');

                if (confirm(`Are you sure you want to cancel the invitation to ${row.querySelector('.user-name').textContent}?`)) {
                    // Simulate loading state
                    this.disabled = true;

                    // In a real implementation, you would call an API here
                    setTimeout(() => {
                        // Update status cell
                        const statusCell = row.querySelector('td:nth-child(6)');
                        statusCell.innerHTML = '<span class="invitation-status canceled">Canceled</span>';

                        // Update action buttons
                        const actionsCell = row.querySelector('td:nth-child(7)');
                        actionsCell.innerHTML = `
                            <div class="invitation-actions">
                                <button class="action-btn resend-invitation" data-id="${id}" title="Resend">
                                    <span class="material-icons">send</span>
                                </button>
                                <button class="action-btn delete-invitation" data-id="${id}" title="Delete">
                                    <span class="material-icons">delete</span>
                                </button>
                            </div>
                        `;

                        alert('Invitation canceled successfully');
                    }, 1000);
                }
            });
        });

        deleteButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const row = this.closest('tr');

                if (confirm(`Are you sure you want to delete the invitation to ${row.querySelector('.user-name').textContent}?`)) {
                    // Simulate loading state
                    this.disabled = true;

                    // In a real implementation, you would call an API here
                    setTimeout(() => {
                        row.remove();
                        alert('Invitation deleted successfully');
                    }, 1000);
                }
            });
        });

        // Helper function to get CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // Invitation form submission
        const invitationForm = document.getElementById('invitation-form');

        if (invitationForm) {
            invitationForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Get the form data
                const formData = new FormData(this);

                // Log all form fields to debug
                console.log("Form data collected:");
                for (let pair of formData.entries()) {
                    console.log(pair[0] + ': ' + pair[1]);
                }

                // Get form field values directly from DOM for comparison
                const firstName = document.getElementById('first_name');
                const lastName = document.getElementById('last_name');
                const email = document.getElementById('email');
                const role = document.getElementById('role');

                console.log("Direct DOM values:");
                console.log("first_name:", firstName);
                console.log("last_name:", lastName);
                console.log("email:", email);
                console.log("role:", role);

                // Check if required fields are filled
                if (!firstName || !lastName || !email || !role) {
                    console.log("Validation failed - missing fields");
                    alert("Please fill in all required fields");
                    return;
                }

                console.log("Validation passed, proceeding with form submission");

                // Disable submit button to prevent double submission
                const submitBtn = document.getElementById('send-invitation');
                submitBtn.disabled = true;
                submitBtn.textContent = 'Sending...';

                // Create a standard form submission with proper Content-Type
                fetch('/settings/permissions/invite/', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                        // Don't set Content-Type - FormData will set it with the boundary
                    }
                })
                .then(response => {
                    console.log("Response status:", response.status);
                    return response.json();
                })
                .then(data => {
                    console.log("Response data:", data);
                    if (data.success) {
                        alert('Invitation sent successfully!');

                        // Close modal and reset form
                        invitationForm.reset();
                        document.getElementById('invitation-modal').style.display = 'none';

                        // Refresh the page to see updated list
                        window.location.reload();
                    } else {
                        let errorMessage = 'Failed to send invitation';
                        if (data.errors) {
                            errorMessage += ': ' + JSON.stringify(data.errors);
                        }
                        alert(errorMessage);
                    }

                    // Reset button
                    submitBtn.disabled = false;
                    submitBtn.textContent = 'Send Invitation';
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while sending the invitation: ' + error.message);

                    // Reset button
                    submitBtn.disabled = false;
                    submitBtn.textContent = 'Send Invitation';
                });
            });
        }

        // Role change form submission
        const roleChangeForm = document.getElementById('role-change-form');

        if (roleChangeForm) {
            roleChangeForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const newRole = document.getElementById('new-role').value;

                // Validate form
                if (!newRole) {
                    alert('Please select a new role.');
                    return;
                }

                // Disable submit button to prevent double submission
                const submitBtn = document.getElementById('confirm-role-change');
                submitBtn.disabled = true;
                submitBtn.textContent = 'Changing...';

                // In a real implementation, you would call an API here
                setTimeout(() => {
                    alert('Role changed successfully');

                    // Close modal and reset form
                    roleChangeForm.reset();
                    closeModal(roleChangeModal);

                    // Reset button
                    submitBtn.disabled = false;
                    submitBtn.textContent = 'Change Role';

                    // Refresh table (in a real implementation, you would fetch updated data)
                }, 1500);
            });
        }

        // Dynamic permissions preview based on role selection
        const roleSelect = document.getElementById('role');
        const permissionsPreview = document.querySelector('.permissions-preview');

    });
</script>
{% endblock %}
