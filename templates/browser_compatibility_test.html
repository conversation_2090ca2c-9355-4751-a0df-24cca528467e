{% extends 'main.html' %}
{% load static %}
{% load i18n %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Browser Compatibility Test</h1>
        
        <!-- External Resource Loading Test -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">External Resource Loading Test</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Google Fonts Test -->
                <div class="border rounded p-4">
                    <h3 class="font-medium mb-2">Google Material Icons</h3>
                    <div class="flex items-center space-x-2">
                        <span class="material-icons text-green-600">check_circle</span>
                        <span id="material-icons-status">Loading...</span>
                    </div>
                </div>
                
                <!-- Bootstrap Icons Test -->
                <div class="border rounded p-4">
                    <h3 class="font-medium mb-2">Bootstrap Icons</h3>
                    <div class="flex items-center space-x-2">
                        <i class="bi bi-check-circle text-green-600"></i>
                        <span id="bootstrap-icons-status">Loading...</span>
                    </div>
                </div>
                
                <!-- FontAwesome Test -->
                <div class="border rounded p-4">
                    <h3 class="font-medium mb-2">FontAwesome Icons</h3>
                    <div class="flex items-center space-x-2">
                        <i class="fa fa-check-circle text-green-600"></i>
                        <span id="fontawesome-status">Loading...</span>
                    </div>
                </div>
                
                <!-- jQuery Test -->
                <div class="border rounded p-4">
                    <h3 class="font-medium mb-2">jQuery Library</h3>
                    <div class="flex items-center space-x-2">
                        <span id="jquery-icon" class="text-green-600">✓</span>
                        <span id="jquery-status">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- PDF Display Test -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">PDF Display Test</h2>
            
            <div class="mb-4">
                <p class="text-gray-600 mb-4">Testing PDF display compatibility across different browsers:</p>
                <div id="browser-info" class="bg-gray-100 p-3 rounded text-sm mb-4"></div>
            </div>
            
            <!-- Sample PDF Display -->
            <div class="border border-gray-200 rounded-lg overflow-hidden">
                <div class="p-4 bg-gray-50" style="height: 400px;">
                    <!-- PDF Viewer Controls -->
                    <div class="flex justify-between items-center mb-4 bg-white p-3 rounded-lg shadow-sm">
                        <div class="flex items-center space-x-2">
                            <svg class="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-sm font-medium text-gray-700">Sample PDF</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="openPdfInNewTab('{% static 'cv/no_cv.pdf' %}')"
                                    class="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                                Open in New Tab
                            </button>
                        </div>
                    </div>
                    
                    <!-- Cross-browser PDF Display -->
                    <div id="pdf-container" class="bg-white rounded-lg shadow-sm" style="height: 300px;">
                        <!-- Primary: iframe for better browser compatibility -->
                        <iframe id="pdf-iframe"
                                src="{% static 'cv/no_cv.pdf' %}"
                                width="100%"
                                height="100%"
                                style="border: none; border-radius: 8px;"
                                onload="handlePdfLoad()"
                                onerror="handlePdfError()">
                        </iframe>

                        <!-- Fallback: object tag -->
                        <object id="pdf-object"
                                data="{% static 'cv/no_cv.pdf' %}"
                                type="application/pdf"
                                width="100%"
                                height="100%"
                                style="display: none; border-radius: 8px;">
                            <!-- Final fallback: download link -->
                            <div id="pdf-fallback" class="flex flex-col items-center justify-center h-full text-center p-8">
                                <svg class="w-16 h-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">PDF Preview Not Available</h3>
                                <p class="text-gray-600 mb-4">Your browser doesn't support PDF preview.</p>
                                <a href="{% static 'cv/no_cv.pdf' %}"
                                   target="_blank"
                                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                    Download PDF
                                </a>
                            </div>
                        </object>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">Test Results</h2>
            <div id="test-results" class="space-y-2">
                <!-- Results will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- Include Material Icons for testing -->
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Display browser information
    const browserInfo = document.getElementById('browser-info');
    const userAgent = navigator.userAgent;
    const isChrome = userAgent.includes('Chrome') && !userAgent.includes('Edge');
    const isFirefox = userAgent.includes('Firefox');
    const isSafari = userAgent.includes('Safari') && !userAgent.includes('Chrome');
    const isOpera = userAgent.includes('Opera') || userAgent.includes('OPR');
    const isEdge = userAgent.includes('Edge') || userAgent.includes('Edg');
    
    let browserName = 'Unknown';
    if (isChrome) browserName = 'Chrome';
    else if (isFirefox) browserName = 'Firefox';
    else if (isSafari) browserName = 'Safari';
    else if (isOpera) browserName = 'Opera';
    else if (isEdge) browserName = 'Edge';
    
    browserInfo.innerHTML = `
        <strong>Browser:</strong> ${browserName}<br>
        <strong>User Agent:</strong> ${userAgent}
    `;
    
    // Test external resources
    setTimeout(function() {
        testExternalResources();
    }, 2000);
});

function testExternalResources() {
    const results = [];
    
    // Test Material Icons
    const materialIcon = document.querySelector('.material-icons');
    const materialIconsStatus = document.getElementById('material-icons-status');
    if (materialIcon && window.getComputedStyle(materialIcon).fontFamily.includes('Material Icons')) {
        materialIconsStatus.textContent = 'Loaded ✓';
        materialIconsStatus.className = 'text-green-600';
        results.push('✓ Material Icons loaded successfully');
    } else {
        materialIconsStatus.textContent = 'Failed ✗';
        materialIconsStatus.className = 'text-red-600';
        results.push('✗ Material Icons failed to load');
    }
    
    // Test Bootstrap Icons
    const bootstrapIcon = document.querySelector('.bi');
    const bootstrapIconsStatus = document.getElementById('bootstrap-icons-status');
    if (bootstrapIcon && window.getComputedStyle(bootstrapIcon).fontFamily.includes('bootstrap-icons')) {
        bootstrapIconsStatus.textContent = 'Loaded ✓';
        bootstrapIconsStatus.className = 'text-green-600';
        results.push('✓ Bootstrap Icons loaded successfully');
    } else {
        bootstrapIconsStatus.textContent = 'Failed ✗';
        bootstrapIconsStatus.className = 'text-red-600';
        results.push('✗ Bootstrap Icons failed to load');
    }
    
    // Test FontAwesome
    const faIcon = document.querySelector('.fa');
    const fontawesomeStatus = document.getElementById('fontawesome-status');
    if (faIcon && window.getComputedStyle(faIcon).fontFamily.includes('FontAwesome')) {
        fontawesomeStatus.textContent = 'Loaded ✓';
        fontawesomeStatus.className = 'text-green-600';
        results.push('✓ FontAwesome loaded successfully');
    } else {
        fontawesomeStatus.textContent = 'Failed ✗';
        fontawesomeStatus.className = 'text-red-600';
        results.push('✗ FontAwesome failed to load');
    }
    
    // Test jQuery
    const jqueryStatus = document.getElementById('jquery-status');
    if (typeof jQuery !== 'undefined') {
        jqueryStatus.textContent = 'Loaded ✓';
        jqueryStatus.className = 'text-green-600';
        results.push('✓ jQuery loaded successfully');
    } else {
        jqueryStatus.textContent = 'Failed ✗';
        jqueryStatus.className = 'text-red-600';
        results.push('✗ jQuery failed to load');
    }
    
    // Display results
    const testResults = document.getElementById('test-results');
    testResults.innerHTML = results.map(result => `<div class="p-2 bg-gray-50 rounded">${result}</div>`).join('');
}

// PDF handling functions
function openPdfInNewTab(pdfUrl) {
    window.open(pdfUrl, '_blank');
}

function handlePdfLoad() {
    console.log('PDF loaded successfully in iframe');
    document.getElementById('pdf-object').style.display = 'none';
}

function handlePdfError() {
    console.log('PDF failed to load in iframe, trying object fallback');
    document.getElementById('pdf-iframe').style.display = 'none';
    document.getElementById('pdf-object').style.display = 'block';
}
</script>
{% endblock content %}
